import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, Bo<PERSON>, Trophy, Loader2 } from 'lucide-react';
import { QuizCategory } from '../../types';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import toast from 'react-hot-toast';

interface GameModeSelectorProps {
  selectedCategory: QuizCategory;
  onBack: () => void;
}

export const GameModeSelector: React.FC<GameModeSelectorProps> = ({ 
  selectedCategory, 
  onBack 
}) => {
  const navigate = useNavigate();
  const [roomCode, setRoomCode] = useState('');
  const [showJoinRoom, setShowJoinRoom] = useState(false);
  const [loading, setLoading] = useState<string | null>(null);

  const { user } = useAuthStore();
  const { 
    startMatchmaking, 
    startSoloBattle, 
    createFriendBattle, 
    joinFriendBattle,
    isSearching,
    isLoading: gameLoading,
    resetGame
  } = useGameStore();

  const handleQuickBattle = async () => {
    if (!user) {
      toast.error('Please sign in to start a battle');
      return;
    }
    
    if (isSearching || gameLoading) {
      toast.error('Already searching for a match');
      return;
    }
    
    console.log('🎯 Quick Battle clicked - starting matchmaking process...');
    setLoading('quick');
    
    try {
      // Reset any previous game state
      resetGame();
      
      // Navigate to matchmaking page first
      console.log('🚀 Navigating to matchmaking page...');
      navigate('/matchmaking');
      
      // Start matchmaking process after navigation
      console.log('📝 Starting matchmaking for category:', selectedCategory);
      await startMatchmaking(selectedCategory, user.id);
      
    } catch (error: any) {
      console.error('❌ Quick battle error:', error);
      
      // Force cleanup of any stuck state
      const { forceCloseMatchmaking } = useGameStore.getState();
      forceCloseMatchmaking();
      
      // Navigate back to home if there's an error
      navigate('/');
      
      // Provide specific error messages
      let errorMessage = 'Failed to start matchmaking';
      if (error.message?.includes('profile')) {
        errorMessage = 'Profile setup required. Please refresh and try again.';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message?.includes('session')) {
        errorMessage = 'Session expired. Please refresh and sign in again.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
      
    } finally {
      // Always clear loading state
      setLoading(null);
    }
  };

  const handleSoloBattle = async () => {
    if (!user) {
      toast.error('Please sign in to start a battle');
      return;
    }
    
    setLoading('solo');
    try {
      // Reset any previous game state
      resetGame();
      
      await startSoloBattle(selectedCategory, user.id);
      
      // Navigate to battle page
      const battle = useGameStore.getState().battle;
      if (battle) {
        navigate(`/battle/${battle.id}`);
      } else {
        throw new Error('Failed to create solo battle');
      }
    } catch (error: any) {
      console.error('Solo battle error:', error);
      toast.error('Failed to start solo battle: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(null);
    }
  };

  const handleCreateRoom = async () => {
    if (!user) {
      toast.error('Please sign in to create a room');
      return;
    }
    
    setLoading('create');
    try {
      // Reset any previous game state
      resetGame();
      
      const code = await createFriendBattle(selectedCategory, user.id);
      toast.success(`Room created! Code: ${code}`);
      
      // Copy to clipboard
      try {
        await navigator.clipboard.writeText(code);
        toast.success('Room code copied to clipboard!');
      } catch (clipboardError) {
        console.warn('Failed to copy to clipboard:', clipboardError);
      }

      // Navigate to battle page
      const battle = useGameStore.getState().battle;
      if (battle) {
        navigate(`/battle/${battle.id}`);
      } else {
        throw new Error('Failed to create friend battle');
      }
    } catch (error: any) {
      console.error('Create room error:', error);
      toast.error('Failed to create room: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(null);
    }
  };

  const handleJoinRoom = async () => {
    if (!user) {
      toast.error('Please sign in to join a room');
      return;
    }
    
    if (!roomCode.trim()) {
      toast.error('Please enter a room code');
      return;
    }
    
    setLoading('join');
    try {
      // Reset any previous game state
      resetGame();
      
      await joinFriendBattle(roomCode.trim().toUpperCase(), user.id);
      setRoomCode('');
      setShowJoinRoom(false);
      
      // Navigate to battle page
      const battle = useGameStore.getState().battle;
      if (battle) {
        navigate(`/battle/${battle.id}`);
      } else {
        throw new Error('Failed to join friend battle');
      }
    } catch (error: any) {
      console.error('Join room error:', error);
      toast.error('Failed to join room: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(null);
    }
  };

  const gameModes = [
    {
      id: 'quick',
      title: 'Quick Battle',
      description: 'Find a random opponent instantly',
      icon: Zap,
      color: 'from-orange-500 to-red-500',
      action: handleQuickBattle,
      disabled: isSearching || gameLoading,
    },
    {
      id: 'solo',
      title: 'Solo Battle',
      description: 'Challenge the Bot and improve',
      icon: Bot,
      color: 'from-blue-500 to-purple-500',
      action: handleSoloBattle,
      disabled: false,
    },
    {
      id: 'create',
      title: 'Create Room',
      description: 'Play with friends using room code',
      icon: Users,
      color: 'from-green-500 to-teal-500',
      action: handleCreateRoom,
      disabled: false,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center space-x-2 mb-4"
        >
          <Trophy className="w-8 h-8 text-yellow-500" />
          <h2 className="text-2xl font-bold text-gray-900">
            Choose Your Battle Mode
          </h2>
        </motion.div>
        <p className="text-gray-600">
          Selected: <span className="font-semibold text-purple-600">{selectedCategory}</span>
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {gameModes.map((mode, index) => {
          const Icon = mode.icon;
          const isLoading = loading === mode.id;
          const isDisabled = mode.disabled || isLoading;
          
          return (
            <motion.div
              key={mode.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card hover={!isDisabled} className={`p-6 text-center ${isDisabled ? 'opacity-60' : ''}`}>
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${mode.color} flex items-center justify-center`}>
                  <Icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {mode.title}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {mode.description}
                </p>
                <Button
                  onClick={mode.action}
                  disabled={isDisabled}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    mode.title
                  )}
                </Button>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Join Room Section */}
      <Card className="p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Join Friend's Room
          </h3>
          
          {!showJoinRoom ? (
            <Button
              variant="outline"
              onClick={() => setShowJoinRoom(true)}
              disabled={loading !== null}
            >
              Enter Room Code
            </Button>
          ) : (
            <div className="flex space-x-2 max-w-xs mx-auto">
              <input
                type="text"
                placeholder="Room Code"
                value={roomCode}
                onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-center font-mono"
                maxLength={6}
                disabled={loading === 'join'}
              />
              <Button
                onClick={handleJoinRoom}
                disabled={!roomCode.trim() || loading === 'join'}
              >
                {loading === 'join' ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  'Join'
                )}
              </Button>
            </div>
          )}
        </div>
      </Card>

      <div className="text-center">
        <Button variant="ghost" onClick={onBack} disabled={loading !== null}>
          ← Back to Categories
        </Button>
      </div>
    </div>
  );
};