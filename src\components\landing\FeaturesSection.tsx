import React from 'react';
import { motion } from 'framer-motion';
import { Swords, Film, Code, Brain, Calculator, Users, Trophy, Zap } from 'lucide-react';
import { Card } from '../ui/Card';

export const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: Swords,
      title: 'Real-Time 1v1 Battles',
      description: 'Challenge players worldwide in intense real-time quiz duels with live scoring and instant results.',
      color: 'from-red-500 to-pink-500',
    },
    {
      icon: Brain,
      title: '4 Quiz Categories',
      description: 'Master Cinema, Coding, Math, and General Knowledge with thousands of expertly crafted questions.',
      color: 'from-blue-500 to-indigo-500',
      subIcons: [Film, Code, Calculator, Brain],
    },
    {
      icon: Users,
      title: 'Play with Friends',
      description: 'Create private rooms and battle your friends using unique room codes for exclusive competitions.',
      color: 'from-green-500 to-teal-500',
    },
    {
      icon: Trophy,
      title: 'Daily Rewards',
      description: 'Earn up to ₹100 daily through victories, streaks, and leaderboard achievements.',
      color: 'from-yellow-500 to-orange-500',
    },
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Why Choose <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">QuizBattle Pro?</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the ultimate quiz gaming platform with cutting-edge features designed for competitive learning and fun
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card hover className="p-8 text-center h-full group">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                    className={`w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                  >
                    <Icon className="w-10 h-10 text-white" />
                  </motion.div>

                  {feature.subIcons && (
                    <div className="flex justify-center space-x-2 mb-4">
                      {feature.subIcons.map((SubIcon, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ scale: 0 }}
                          whileInView={{ scale: 1 }}
                          transition={{ duration: 0.3, delay: 0.5 + idx * 0.1 }}
                          viewport={{ once: true }}
                          className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
                        >
                          <SubIcon className="w-4 h-4 text-gray-600" />
                        </motion.div>
                      ))}
                    </div>
                  )}

                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Lightning Fast</h4>
            <p className="text-gray-600">15-second questions keep the adrenaline pumping</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Trophy className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Global Rankings</h4>
            <p className="text-gray-600">Compete on daily and weekly leaderboards</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Social Gaming</h4>
            <p className="text-gray-600">Challenge friends and make new connections</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};