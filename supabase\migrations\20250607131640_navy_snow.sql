/*
  # Fix Foreign Key Constraint Issues

  1. Ensure profiles table has proper RLS policies
  2. Create trigger to automatically create profiles entry
  3. Add just-in-time profile creation for matchmaking
  4. Update RLS policies to allow profile creation
*/

-- Ensure profiles table has RLS enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies on profiles table
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can read all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Create comprehensive RLS policies for profiles table
CREATE POLICY "Users can insert own profile"
  ON public.profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can read all profiles"
  ON public.profiles
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update own profile"
  ON public.profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Create or replace function to handle new user creation in both tables
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Insert into users table
  INSERT INTO public.users (
    id, 
    email, 
    username, 
    profile_picture_url,
    total_coins,
    total_score,
    daily_score,
    weekly_score,
    matches_played,
    matches_won,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(
      NEW.raw_user_meta_data->>'username', 
      split_part(NEW.email, '@', 1),
      'User'
    ),
    NEW.raw_user_meta_data->>'avatar_url',
    100, -- Default starting coins
    0,   -- Default total score
    0,   -- Default daily score
    0,   -- Default weekly score
    0,   -- Default matches played
    0,   -- Default matches won
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;
  
  -- Insert into profiles table (required for foreign key constraints)
  INSERT INTO public.profiles (
    id,
    username,
    email,
    avatar_url,
    total_wins,
    total_games,
    best_category,
    current_rank,
    created_at
  )
  VALUES (
    NEW.id,
    COALESCE(
      NEW.raw_user_meta_data->>'username', 
      split_part(NEW.email, '@', 1),
      'User'
    ),
    NEW.email,
    NEW.raw_user_meta_data->>'avatar_url',
    0,   -- Default total wins
    0,   -- Default total games
    NULL, -- No best category yet
    0,   -- Default rank
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the auth process
    RAISE WARNING 'Failed to create user/profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to ensure profile exists before matchmaking operations
CREATE OR REPLACE FUNCTION public.ensure_user_profile_exists(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if profile exists, if not create it
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id) THEN
    -- Get user data from auth.users
    INSERT INTO public.profiles (
      id,
      username,
      email,
      avatar_url,
      total_wins,
      total_games,
      best_category,
      current_rank,
      created_at
    )
    SELECT 
      au.id,
      COALESCE(
        au.raw_user_meta_data->>'username',
        split_part(au.email, '@', 1),
        'User'
      ),
      au.email,
      au.raw_user_meta_data->>'avatar_url',
      0,
      0,
      NULL,
      0,
      NOW()
    FROM auth.users au
    WHERE au.id = user_id
    ON CONFLICT (id) DO NOTHING;
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to ensure profile exists for user %: %', user_id, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update matchmaking_queue policies to ensure profile exists
DROP POLICY IF EXISTS "Users can insert own queue entry" ON public.matchmaking_queue;
DROP POLICY IF EXISTS "Users can read all queue entries" ON public.matchmaking_queue;
DROP POLICY IF EXISTS "Users can delete own queue entry" ON public.matchmaking_queue;

-- Create new policies with profile existence check
CREATE POLICY "Users can insert own queue entry"
  ON public.matchmaking_queue
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id AND 
    public.ensure_user_profile_exists(auth.uid())
  );

CREATE POLICY "Users can read all queue entries"
  ON public.matchmaking_queue
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can delete own queue entry"
  ON public.matchmaking_queue
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Ensure RLS is enabled on matchmaking_queue
ALTER TABLE public.matchmaking_queue ENABLE ROW LEVEL SECURITY;