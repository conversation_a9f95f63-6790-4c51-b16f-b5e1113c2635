import { supabase } from '../lib/supabase';

export const testMatchmakingSystem = async () => {
  console.log('🧪 Testing matchmaking system...');
  
  try {
    // Test 1: Check if tables exist
    console.log('📋 Test 1: Checking table existence...');
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['matchmaking_queue', 'matches', 'questions', 'profiles']);
    
    if (tablesError) {
      console.error('❌ Error checking tables:', tablesError);
      return false;
    }
    
    console.log('✅ Available tables:', tables?.map(t => t.table_name));
    
    // Test 2: Check matchmaking_queue structure
    console.log('📋 Test 2: Checking matchmaking_queue structure...');
    
    const { data: queueColumns, error: queueError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'matchmaking_queue')
      .eq('table_schema', 'public');
    
    if (queueError) {
      console.error('❌ Error checking queue structure:', queueError);
    } else {
      console.log('✅ Queue columns:', queueColumns);
    }
    
    // Test 3: Try to insert a test entry
    console.log('📋 Test 3: Testing queue operations...');
    
    const testUserId = 'test-user-' + Date.now();
    
    const { data: insertData, error: insertError } = await supabase
      .from('matchmaking_queue')
      .insert({
        user_id: testUserId,
        category: 'test',
        created_at: new Date().toISOString(),
        is_active: true,
        last_active_at: new Date().toISOString()
      })
      .select();
    
    if (insertError) {
      console.error('❌ Error inserting test entry:', insertError);
      return false;
    }
    
    console.log('✅ Test entry inserted:', insertData);
    
    // Test 4: Try to query the entry
    const { data: queryData, error: queryError } = await supabase
      .from('matchmaking_queue')
      .select('*')
      .eq('user_id', testUserId);
    
    if (queryError) {
      console.error('❌ Error querying test entry:', queryError);
    } else {
      console.log('✅ Test entry queried:', queryData);
    }
    
    // Test 5: Clean up test entry
    const { error: deleteError } = await supabase
      .from('matchmaking_queue')
      .delete()
      .eq('user_id', testUserId);
    
    if (deleteError) {
      console.error('❌ Error deleting test entry:', deleteError);
    } else {
      console.log('✅ Test entry cleaned up');
    }
    
    // Test 6: Check matches table
    console.log('📋 Test 6: Checking matches table...');
    
    const { data: matchesColumns, error: matchesError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'matches')
      .eq('table_schema', 'public');
    
    if (matchesError) {
      console.error('❌ Error checking matches structure:', matchesError);
    } else {
      console.log('✅ Matches columns:', matchesColumns);
    }
    
    console.log('🎉 Matchmaking system test completed!');
    return true;
    
  } catch (error) {
    console.error('💥 Test failed with exception:', error);
    return false;
  }
};

// Function to test real-time subscriptions
export const testRealtimeConnection = async () => {
  console.log('🧪 Testing real-time connection...');
  
  try {
    const channel = supabase
      .channel('test-channel')
      .on('broadcast', { event: 'test' }, (payload) => {
        console.log('📡 Test broadcast received:', payload);
      })
      .subscribe((status, err) => {
        console.log('📡 Test channel status:', status);
        if (err) {
          console.error('📡 Test channel error:', err);
        }
      });
    
    // Send a test broadcast after 2 seconds
    setTimeout(() => {
      channel.send({
        type: 'broadcast',
        event: 'test',
        payload: { message: 'Hello from test!' }
      });
    }, 2000);
    
    // Clean up after 5 seconds
    setTimeout(() => {
      channel.unsubscribe();
      console.log('✅ Test channel cleaned up');
    }, 5000);
    
  } catch (error) {
    console.error('💥 Real-time test failed:', error);
  }
};

// Function to manually test matchmaking between two users
export const testTwoUserMatchmaking = async (user1Id: string, user2Id: string, category: string) => {
  console.log('🧪 Testing two-user matchmaking...');
  
  try {
    // User 1 joins queue
    console.log('👤 User 1 joining queue...');
    const { data: user1Entry, error: user1Error } = await supabase
      .from('matchmaking_queue')
      .insert({
        user_id: user1Id,
        category,
        created_at: new Date().toISOString(),
        is_active: true,
        last_active_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (user1Error) {
      console.error('❌ User 1 join error:', user1Error);
      return;
    }
    
    console.log('✅ User 1 in queue:', user1Entry);
    
    // Wait 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // User 2 joins queue
    console.log('👤 User 2 joining queue...');
    const { data: user2Entry, error: user2Error } = await supabase
      .from('matchmaking_queue')
      .insert({
        user_id: user2Id,
        category,
        created_at: new Date().toISOString(),
        is_active: true,
        last_active_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (user2Error) {
      console.error('❌ User 2 join error:', user2Error);
      return;
    }
    
    console.log('✅ User 2 in queue:', user2Entry);
    
    // Check if they can find each other
    const { data: availableForUser1, error: findError1 } = await supabase
      .from('matchmaking_queue')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .neq('user_id', user1Id)
      .limit(1);
    
    if (findError1) {
      console.error('❌ Find error for user 1:', findError1);
    } else {
      console.log('🔍 Available matches for user 1:', availableForUser1);
    }
    
    // Clean up
    await supabase.from('matchmaking_queue').delete().in('user_id', [user1Id, user2Id]);
    console.log('✅ Test cleanup completed');
    
  } catch (error) {
    console.error('💥 Two-user test failed:', error);
  }
};
