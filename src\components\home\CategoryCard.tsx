import React from 'react';
import { motion } from 'framer-motion';
import { Film, Code, Brain, Calculator } from 'lucide-react';
import { QuizCategory } from '../../types';
import { Card } from '../ui/Card';

interface CategoryCardProps {
  category: QuizCategory;
  onSelect: (category: QuizCategory) => void;
}

const categoryIcons = {
  [QuizCategory.CINEMA]: Film,
  [QuizCategory.CODING]: Code,
  [QuizCategory.GENERAL]: Brain,
  [QuizCategory.MATH]: Calculator,
};

const categoryColors = {
  [QuizCategory.CINEMA]: 'from-red-500 to-pink-500',
  [QuizCategory.CODING]: 'from-green-500 to-teal-500',
  [QuizCategory.GENERAL]: 'from-blue-500 to-indigo-500',
  [QuizCategory.MATH]: 'from-purple-500 to-violet-500',
};

const categoryNames = {
  [QuizCategory.CINEMA]: 'Cinema',
  [QuizCategory.CODING]: 'Coding',
  [QuizCategory.GENERAL]: 'General Knowledge',
  [QuizCategory.MATH]: 'Math',
};

export const CategoryCard: React.FC<CategoryCardProps> = ({ category, onSelect }) => {
  const Icon = categoryIcons[category];
  const colorClass = categoryColors[category];
  const name = categoryNames[category];

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card 
        hover 
        onClick={() => onSelect(category)}
        className="p-6 cursor-pointer group"
      >
        <div className="text-center">
          <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${colorClass} flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
            <Icon className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{name}</h3>
          <p className="text-sm text-gray-600">
            Test your knowledge in {name.toLowerCase()}
          </p>
        </div>
      </Card>
    </motion.div>
  );
};