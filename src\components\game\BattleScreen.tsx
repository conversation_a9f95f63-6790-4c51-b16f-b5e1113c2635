import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Crown, Zap, CheckCircle, XCircle, Timer, ArrowLeft } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Timer as TimerComponent } from '../ui/Timer';
import Confetti from 'react-confetti';

export const BattleScreen: React.FC = () => {
  const [showResults, setShowResults] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const { user } = useAuthStore();
  const {
    battle,
    currentQuestionData,
    currentQuestion,
    score,
    opponentScore,
    isAnswered,
    selectedAnswer,
    timeLeft,
    answerQuestion,
    nextQuestion,
    endBattle,
    resetGame,
    cleanupSubscriptions,
  } = useGameStore();

  useEffect(() => {
    if (!battle || !currentQuestionData) return;

    // Auto-advance to next question after 3 seconds
    if (isAnswered) {
      const timer = setTimeout(() => {
        if (currentQuestion < 9) {
          nextQuestion();
        } else {
          setShowResults(true);
          if (score > opponentScore) {
            setShowConfetti(true);
            setTimeout(() => setShowConfetti(false), 5000);
          }
        }
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isAnswered, currentQuestion, nextQuestion, score, opponentScore]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      cleanupSubscriptions();
    };
  }, [cleanupSubscriptions]);

  const handleAnswer = (answerIndex: number) => {
    if (!user || !battle || isAnswered) return;
    answerQuestion(answerIndex, user.id);
  };

  const handleTimeUp = () => {
    if (!isAnswered) {
      handleAnswer(-1); // No answer selected
    }
  };

  const handleEndBattle = () => {
    endBattle();
    resetGame();
  };

  const handleQuitBattle = () => {
    if (window.confirm('Are you sure you want to quit this battle? You will lose your progress.')) {
      endBattle();
      resetGame();
    }
  };

  if (!battle || !currentQuestionData) return null;

  const isWinner = score > opponentScore;
  const isTie = score === opponentScore;
  const correctAnswerIndex = parseInt(currentQuestionData.correct_answer);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 p-4">
      {showConfetti && <Confetti />}
      
      <div className="max-w-4xl mx-auto">
        {/* Battle Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white rounded-xl shadow-lg p-4 mb-6"
        >
          <div className="flex items-center justify-between">
            {/* Quit button */}
            <button
              onClick={handleQuitBattle}
              className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="text-sm">Quit</span>
            </button>

            <div className="flex items-center space-x-8">
              {/* Player 1 Score */}
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{score}</div>
                <div className="text-sm text-gray-600">{user?.username}</div>
                <div className="text-xs text-gray-500">You</div>
              </div>
              
              {/* Question Counter */}
              <div className="text-center">
                <Crown className="w-8 h-8 text-yellow-500 mx-auto mb-1" />
                <div className="text-lg font-semibold text-gray-700">
                  {currentQuestion + 1}/10
                </div>
                <div className="text-xs text-gray-500">
                  {currentQuestionData.category}
                </div>
              </div>
              
              {/* Player 2 Score */}
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">{opponentScore}</div>
                <div className="text-sm text-gray-600">
                  {battle.is_bot_match || battle.is_solo_match ? 'Bot' : 'Opponent'}
                </div>
                <div className="text-xs text-gray-500">Enemy</div>
              </div>
            </div>

            {/* Timer */}
            <TimerComponent
              duration={15}
              onTimeUp={handleTimeUp}
              isActive={!isAnswered}
            />
          </div>
        </motion.div>

        {/* Question Card */}
        <AnimatePresence mode="wait">
          {!showResults ? (
            <motion.div
              key={currentQuestion}
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="p-6 mb-6">
                <div className="text-center mb-6">
                  <div className="text-sm text-purple-600 font-medium mb-2">
                    Question {currentQuestion + 1} of 10
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {currentQuestionData.question}
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentQuestionData.options.map((option, index) => {
                    let buttonStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-900 border-2 border-transparent';
                    
                    if (isAnswered) {
                      if (index === correctAnswerIndex) {
                        buttonStyle = 'bg-green-500 text-white border-green-600'; // Correct answer
                      } else if (index === selectedAnswer && index !== correctAnswerIndex) {
                        buttonStyle = 'bg-red-500 text-white border-red-600'; // Wrong selected answer
                      } else {
                        buttonStyle = 'bg-gray-200 text-gray-600 border-gray-300'; // Other options
                      }
                    } else if (selectedAnswer === index) {
                      buttonStyle = 'bg-purple-100 border-purple-500 text-purple-900'; // Selected but not answered yet
                    }

                    return (
                      <motion.button
                        key={index}
                        whileHover={!isAnswered ? { scale: 1.02 } : undefined}
                        whileTap={!isAnswered ? { scale: 0.98 } : undefined}
                        onClick={() => handleAnswer(index)}
                        disabled={isAnswered}
                        className={`p-4 rounded-lg transition-all duration-200 text-left font-medium ${buttonStyle}`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-sm font-bold">
                            {String.fromCharCode(65 + index)}
                          </div>
                          <span className="flex-1">{option}</span>
                          {isAnswered && index === correctAnswerIndex && (
                            <CheckCircle className="w-5 h-5 ml-auto" />
                          )}
                          {isAnswered && index === selectedAnswer && index !== correctAnswerIndex && (
                            <XCircle className="w-5 h-5 ml-auto" />
                          )}
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {isAnswered && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 text-center"
                  >
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Timer className="w-5 h-5 text-gray-500" />
                      <span className="text-gray-600">
                        {currentQuestion < 9 ? 'Next question in 3s' : 'Showing results in 3s'}
                      </span>
                    </div>
                    
                    {selectedAnswer === correctAnswerIndex ? (
                      <div className="text-green-600 font-semibold">
                        ✅ Correct! +1 point
                      </div>
                    ) : selectedAnswer === -1 ? (
                      <div className="text-orange-600 font-semibold">
                        ⏰ Time's up! No points
                      </div>
                    ) : (
                      <div className="text-red-600 font-semibold">
                        ❌ Incorrect! No points
                      </div>
                    )}
                  </motion.div>
                )}
              </Card>
            </motion.div>
          ) : (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center"
            >
              <Card className="p-8">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="mb-6"
                >
                  {isWinner ? (
                    <div className="text-6xl">🏆</div>
                  ) : isTie ? (
                    <div className="text-6xl">🤝</div>
                  ) : (
                    <div className="text-6xl">😔</div>
                  )}
                </motion.div>

                <h2 className={`text-3xl font-bold mb-4 ${
                  isWinner ? 'text-green-600' : isTie ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {isWinner ? 'Victory!' : isTie ? 'Draw!' : 'Defeat!'}
                </h2>

                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-purple-600">{score}</div>
                      <div className="text-sm text-gray-600">Your Score</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-gray-400">VS</div>
                      <div className="text-sm text-gray-600">Battle</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-red-500">{opponentScore}</div>
                      <div className="text-sm text-gray-600">
                        {battle.is_bot_match || battle.is_solo_match ? 'Bot' : 'Opponent'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Performance stats */}
                <div className="bg-blue-50 rounded-lg p-4 mb-6">
                  <div className="text-sm text-gray-600 mb-2">Performance</div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Accuracy:</span>
                    <span className="font-semibold text-blue-600">
                      {Math.round((score / 10) * 100)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Correct Answers:</span>
                    <span className="font-semibold text-green-600">
                      {score}/10
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={handleEndBattle}
                    className="w-full"
                    size="lg"
                  >
                    Return to Home
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};