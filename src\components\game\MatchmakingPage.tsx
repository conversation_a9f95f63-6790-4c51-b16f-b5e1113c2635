import React, { useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Search, Clock, Zap, X, Loader2, Users, Target, ArrowLeft } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

export const MatchmakingPage: React.FC = () => {
  const navigate = useNavigate();
  const { 
    isSearching, 
    searchTimeLeft, 
    stopMatchmaking, 
    forceCloseMatchmaking,
    isLoading,
    battle
  } = useGameStore();
  const { user } = useAuthStore();

  // Redirect to battle if match is found
  useEffect(() => {
    if (battle) {
      console.log('🎮 Battle found, redirecting to battle page:', battle.id);
      navigate(`/battle/${battle.id}`);
    }
  }, [battle, navigate]);

  const handleCancel = useCallback(async () => {
    if (!user) {
      console.warn('⚠️ No user found, forcing UI cleanup');
      forceCloseMatchmaking();
      navigate('/home');
      return;
    }

    console.log('🛑 Cancel button clicked, stopping matchmaking for user:', user.id);
    
    try {
      await stopMatchmaking(user.id);
      navigate('/home');
    } catch (error) {
      console.error('❌ Error during cancel:', error);
      // Force cleanup if normal cancellation fails
      forceCloseMatchmaking();
      navigate('/home');
    }
  }, [user, stopMatchmaking, forceCloseMatchmaking, navigate]);

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isSearching) {
        console.log('⌨️ Escape key pressed, cancelling search');
        handleCancel();
      }
    };

    if (isSearching) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isSearching, handleCancel]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 MatchmakingPage unmounting, cleaning up...');
      const { cleanupSubscriptions } = useGameStore.getState();
      cleanupSubscriptions();
    };
  }, []);

  // Redirect to home if not searching and no battle
  useEffect(() => {
    if (!isSearching && !isLoading && !battle) {
      console.log('🏠 Not searching and no battle, redirecting to home');
      navigate('/home');
    }
  }, [isSearching, isLoading, battle, navigate]);

  // Calculate progress percentage for animations
  const progressPercentage = (searchTimeLeft / 30) * 100;
  const isUrgent = searchTimeLeft <= 10;

  // Show loading state if we're not actively searching but still loading
  if (!isSearching && isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 flex items-center justify-center p-4">
        <Card className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Setting up matchmaking...</p>
        </Card>
      </div>
    );
  }

  // Don't render if not searching and not loading
  if (!isSearching && !isLoading) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Back Button */}
        <motion.button
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={handleCancel}
          disabled={isLoading}
          className="mb-6 flex items-center space-x-2 text-white hover:text-yellow-300 transition-colors duration-200 disabled:opacity-50"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Home</span>
        </motion.button>

        <Card className="p-8 text-center relative overflow-hidden">
          {/* Single animated search icon */}
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 3, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
            className="w-24 h-24 mx-auto mb-8 relative"
          >
            <div className={`w-24 h-24 rounded-full ${isUrgent ? 'bg-red-500' : 'bg-purple-600'} flex items-center justify-center shadow-xl`}>
              <Search className="w-12 h-12 text-white" />
            </div>
          </motion.div>

          <motion.h1 
            animate={{ opacity: [0.8, 1, 0.8] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Finding Opponent...
          </motion.h1>
          
          <p className="text-xl text-gray-600 mb-8">
            Searching for players ready to battle...
          </p>

          {/* Large timer display that shows the actual countdown */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              <Clock className={`w-8 h-8 ${isUrgent ? 'text-red-500' : 'text-purple-500'}`} />
            </motion.div>
            <motion.span 
              key={searchTimeLeft} // Key change triggers animation
              initial={{ scale: 1.2, color: isUrgent ? '#ef4444' : '#8b5cf6' }}
              animate={{ scale: 1, color: isUrgent ? '#ef4444' : '#8b5cf6' }}
              transition={{ duration: 0.3 }}
              className="text-6xl font-bold"
            >
              {searchTimeLeft}s
            </motion.span>
          </div>

          {/* Animated progress bar that reflects actual time */}
          <div className="w-full bg-gray-200 rounded-full h-6 mb-8 overflow-hidden shadow-inner">
            <motion.div
              animate={{ 
                width: `${progressPercentage}%`,
                backgroundColor: isUrgent ? '#ef4444' : '#8b5cf6'
              }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="h-full rounded-full relative"
            >
              {/* Animated shine effect */}
              <motion.div
                animate={{ x: [-100, 200] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 w-20"
              />
            </motion.div>
          </div>

          {/* Status messages with icons */}
          <div className="space-y-4 mb-8">
            <motion.div
              animate={{ opacity: [0.6, 1, 0.6] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="flex items-center justify-center space-x-2 text-lg text-gray-700"
            >
              <motion.div 
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="w-4 h-4 bg-green-500 rounded-full"
              ></motion.div>
              <span className="font-medium">Searching for opponents...</span>
            </motion.div>
            
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Users className="w-5 h-5" />
              <span>Looking for players in your category</span>
            </div>
            
            <motion.div 
              animate={{ opacity: isUrgent ? [0.5, 1, 0.5] : 0.7 }}
              transition={{ duration: 1, repeat: isUrgent ? Infinity : 0 }}
              className={`flex items-center justify-center space-x-2 ${isUrgent ? 'text-orange-600 font-semibold' : 'text-gray-500'}`}
            >
              <Zap className="w-5 h-5" />
              <span>Auto-switching to Solo Battle in {searchTimeLeft}s</span>
            </motion.div>
          </div>

          {/* Cancel button with loading state */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="mb-8"
          >
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
              className="w-full max-w-xs mx-auto"
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Cancelling...
                </>
              ) : (
                <>
                  <X className="w-5 h-5 mr-2" />
                  Cancel Search
                </>
              )}
            </Button>
          </motion.div>

          {/* Tips and info */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 }}
            className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100"
          >
            <div className="flex items-center justify-center space-x-2 mb-3">
              <Target className="w-5 h-5 text-blue-600" />
              <span className="font-semibold text-blue-800">Matchmaking Tips</span>
            </div>
            <div className="space-y-2 text-sm">
              <p className="text-blue-700">
                💡 <strong>Peak hours (6-10 PM):</strong> Faster matchmaking!
              </p>
              <p className="text-blue-700">
                🎯 <strong>30-second timeout:</strong> Auto-switches to Bot battle
              </p>
              <p className="text-blue-600">
                ⌨️ Press <kbd className="px-2 py-1 bg-white rounded text-xs border border-blue-200 font-mono">Esc</kbd> to cancel
              </p>
            </div>
          </motion.div>
        </Card>
      </div>
    </div>
  );
};