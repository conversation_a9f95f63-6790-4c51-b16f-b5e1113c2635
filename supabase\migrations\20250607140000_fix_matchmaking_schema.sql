/*
  # Fix Matchmaking Schema and Real-time Battle System

  1. Schema Updates
    - Add missing columns to matchmaking_queue table
    - Rename battles table to matches for consistency
    - Add proper indexes for performance
    - Update RLS policies

  2. Enhanced Functions
    - Add cleanup functions for stale queue entries
    - Add match creation with proper question assignment
    - Add real-time synchronization support

  3. Performance Improvements
    - Add indexes for faster queries
    - Optimize matchmaking queries
    - Add cleanup procedures
*/

-- Add missing columns to matchmaking_queue table
DO $$
BEGIN
  -- Add is_active column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matchmaking_queue' AND column_name = 'is_active'
  ) THEN
    ALTER TABLE public.matchmaking_queue ADD COLUMN is_active boolean DEFAULT true;
  END IF;
  
  -- Add last_active_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matchmaking_queue' AND column_name = 'last_active_at'
  ) THEN
    ALTER TABLE public.matchmaking_queue ADD COLUMN last_active_at timestamptz DEFAULT now();
  END IF;
END $$;

-- Rename battles table to matches for consistency
DO $$
BEGIN
  -- Check if battles table exists and matches doesn't
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'battles' AND table_schema = 'public'
  ) AND NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'matches' AND table_schema = 'public'
  ) THEN
    ALTER TABLE public.battles RENAME TO matches;
    
    -- Update any existing indexes
    DROP INDEX IF EXISTS idx_battles_status;
    DROP INDEX IF EXISTS idx_battles_room_code;
    CREATE INDEX IF NOT EXISTS idx_matches_status ON public.matches(status);
    CREATE INDEX IF NOT EXISTS idx_matches_room_code ON public.matches(room_code);
  END IF;
END $$;

-- Add additional columns to matches table for real-time sync
DO $$
BEGIN
  -- Add current_question_start_time for synchronized timers
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'current_question_start_time'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN current_question_start_time timestamptz;
  END IF;
  
  -- Add player1_answers and player2_answers for tracking responses
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'player1_answers'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN player1_answers jsonb DEFAULT '[]';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'player2_answers'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN player2_answers jsonb DEFAULT '[]';
  END IF;
  
  -- Add match_type if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'match_type'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN match_type text DEFAULT 'pvp';
  END IF;
  
  -- Add is_bot_match if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'is_bot_match'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN is_bot_match boolean DEFAULT false;
  END IF;
  
  -- Add is_solo_match if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'is_solo_match'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN is_solo_match boolean DEFAULT false;
  END IF;
  
  -- Add questions_assigned if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'questions_assigned'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN questions_assigned jsonb DEFAULT '[]';
  END IF;
  
  -- Add start_time and end_time if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'start_time'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN start_time timestamptz;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'matches' AND column_name = 'end_time'
  ) THEN
    ALTER TABLE public.matches ADD COLUMN end_time timestamptz;
  END IF;
END $$;

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_matchmaking_is_active ON public.matchmaking_queue(is_active);
CREATE INDEX IF NOT EXISTS idx_matchmaking_last_active ON public.matchmaking_queue(last_active_at);
CREATE INDEX IF NOT EXISTS idx_matchmaking_category_active ON public.matchmaking_queue(category, is_active);
CREATE INDEX IF NOT EXISTS idx_matches_player1 ON public.matches(player1_id);
CREATE INDEX IF NOT EXISTS idx_matches_player2 ON public.matches(player2_id);
CREATE INDEX IF NOT EXISTS idx_matches_status_created ON public.matches(status, created_at);

-- Update RLS policies for matches table
DROP POLICY IF EXISTS "Users can read own battles" ON public.matches;
DROP POLICY IF EXISTS "Users can create battles" ON public.matches;
DROP POLICY IF EXISTS "Users can update own battles" ON public.matches;

CREATE POLICY "Users can read own matches" ON public.matches FOR SELECT TO authenticated 
  USING (auth.uid() = player1_id OR auth.uid() = player2_id);
CREATE POLICY "Users can create matches" ON public.matches FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = player1_id);
CREATE POLICY "Users can update own matches" ON public.matches FOR UPDATE TO authenticated 
  USING (auth.uid() = player1_id OR auth.uid() = player2_id);

-- Enable RLS on matches table
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;

-- Create function to cleanup stale matchmaking queue entries
CREATE OR REPLACE FUNCTION public.cleanup_stale_queue_entries()
RETURNS void AS $$
BEGIN
  -- Remove entries older than 5 minutes or marked as inactive
  DELETE FROM public.matchmaking_queue 
  WHERE 
    is_active = false 
    OR last_active_at < NOW() - INTERVAL '5 minutes'
    OR created_at < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update queue entry activity
CREATE OR REPLACE FUNCTION public.update_queue_activity(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.matchmaking_queue 
  SET 
    last_active_at = NOW(),
    is_active = true
  WHERE user_id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
