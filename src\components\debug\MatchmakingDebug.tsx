import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { testTwoUserMatchmaking, testMatchmakingSystem } from '../../utils/testMatchmaking';
import { useAuthStore } from '../../store/authStore';
import { db } from '../../lib/supabase';

export const MatchmakingDebug: React.FC = () => {
  const { user } = useAuthStore();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runSystemTest = async () => {
    setIsRunning(true);
    setResults([]);
    addResult('🧪 Starting system tests...');
    
    try {
      const success = await testMatchmakingSystem();
      addResult(success ? '✅ System test passed' : '❌ System test failed');
    } catch (error) {
      addResult(`❌ System test error: ${error.message}`);
    }
    
    setIsRunning(false);
  };

  const runTwoUserTest = async () => {
    if (!user) {
      addResult('❌ No user logged in');
      return;
    }

    setIsRunning(true);
    addResult('🧪 Starting two-user test...');
    
    try {
      const testUser2Id = 'test-user-2-' + Date.now();
      await testTwoUserMatchmaking(user.id, testUser2Id, 'cinema');
      addResult('✅ Two-user test completed');
    } catch (error) {
      addResult(`❌ Two-user test error: ${error.message}`);
    }
    
    setIsRunning(false);
  };

  const checkCurrentQueue = async () => {
    setIsRunning(true);
    addResult('🔍 Checking current queue...');
    
    try {
      const { data, error } = await db.supabase
        .from('matchmaking_queue')
        .select('*')
        .eq('is_active', true);
      
      if (error) {
        addResult(`❌ Queue check error: ${error.message}`);
      } else {
        addResult(`📊 Active queue entries: ${data?.length || 0}`);
        data?.forEach((entry, index) => {
          addResult(`  ${index + 1}. User: ${entry.user_id.slice(0, 8)}... Category: ${entry.category} Created: ${new Date(entry.created_at).toLocaleTimeString()}`);
        });
      }
    } catch (error) {
      addResult(`❌ Queue check exception: ${error.message}`);
    }
    
    setIsRunning(false);
  };

  const clearQueue = async () => {
    setIsRunning(true);
    addResult('🧹 Clearing queue...');
    
    try {
      const { error } = await db.supabase
        .from('matchmaking_queue')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all
      
      if (error) {
        addResult(`❌ Clear queue error: ${error.message}`);
      } else {
        addResult('✅ Queue cleared');
      }
    } catch (error) {
      addResult(`❌ Clear queue exception: ${error.message}`);
    }
    
    setIsRunning(false);
  };

  const simulateMatch = async () => {
    if (!user) {
      addResult('❌ No user logged in');
      return;
    }

    setIsRunning(true);
    addResult('🎮 Simulating match...');
    
    try {
      // Add current user to queue
      addResult('1. Adding user to queue...');
      await db.joinMatchmaking(user.id, 'cinema');
      
      // Wait 2 seconds
      addResult('2. Waiting 2 seconds...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Add a fake second user
      const fakeUserId = 'fake-user-' + Date.now();
      addResult('3. Adding fake second user...');
      await db.joinMatchmaking(fakeUserId, 'cinema');
      
      // Check if they can find each other
      addResult('4. Checking if users can find each other...');
      const match1 = await db.findAvailableMatch(user.id, 'cinema');
      const match2 = await db.findAvailableMatch(fakeUserId, 'cinema');
      
      addResult(`   User 1 found: ${match1 ? 'YES' : 'NO'}`);
      addResult(`   User 2 found: ${match2 ? 'YES' : 'NO'}`);
      
      // Clean up
      addResult('5. Cleaning up...');
      await db.removeFromMatchmaking(user.id);
      await db.removeFromMatchmaking(fakeUserId);
      
      addResult('✅ Simulation completed');
    } catch (error) {
      addResult(`❌ Simulation error: ${error.message}`);
    }
    
    setIsRunning(false);
  };

  return (
    <Card className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">🐛 Matchmaking Debug Tools</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Button
          onClick={runSystemTest}
          disabled={isRunning}
          variant="outline"
          size="sm"
        >
          System Test
        </Button>
        
        <Button
          onClick={runTwoUserTest}
          disabled={isRunning}
          variant="outline"
          size="sm"
        >
          Two User Test
        </Button>
        
        <Button
          onClick={checkCurrentQueue}
          disabled={isRunning}
          variant="outline"
          size="sm"
        >
          Check Queue
        </Button>
        
        <Button
          onClick={clearQueue}
          disabled={isRunning}
          variant="outline"
          size="sm"
          className="text-red-600 border-red-300"
        >
          Clear Queue
        </Button>
        
        <Button
          onClick={simulateMatch}
          disabled={isRunning}
          variant="outline"
          size="sm"
          className="col-span-2 text-blue-600 border-blue-300"
        >
          Simulate Match
        </Button>
        
        <Button
          onClick={() => setResults([])}
          disabled={isRunning}
          variant="outline"
          size="sm"
          className="col-span-2 text-gray-600 border-gray-300"
        >
          Clear Results
        </Button>
      </div>
      
      <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
        <div className="mb-2 text-gray-400">Debug Console:</div>
        {results.length === 0 ? (
          <div className="text-gray-500">No results yet. Click a button to run tests.</div>
        ) : (
          results.map((result, index) => (
            <div key={index} className="mb-1">
              {result}
            </div>
          ))
        )}
        {isRunning && (
          <div className="text-yellow-400 animate-pulse">Running...</div>
        )}
      </div>
    </Card>
  );
};
