import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface TimerProps {
  duration: number;
  onTimeUp: () => void;
  isActive: boolean;
  syncedTime?: number;
}

export const Timer: React.FC<TimerProps> = ({ duration, onTimeUp, isActive, syncedTime }) => {
  const [timeLeft, setTimeLeft] = useState(syncedTime || duration);

  useEffect(() => {
    if (syncedTime !== undefined) {
      setTimeLeft(syncedTime);
    }
  }, [syncedTime]);

  useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          onTimeUp();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, onTimeUp]);

  useEffect(() => {
    if (syncedTime === undefined) {
      setTimeLeft(duration);
    }
  }, [duration, syncedTime]);

  const percentage = (timeLeft / duration) * 100;
  const isUrgent = timeLeft <= 5;

  return (
    <div className="flex items-center justify-center">
      <div className="relative w-20 h-20">
        <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
          <path
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="2"
          />
          <motion.path
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
            fill="none"
            stroke={isUrgent ? "#ef4444" : "#8b5cf6"}
            strokeWidth="2"
            strokeDasharray="100, 100"
            initial={{ strokeDashoffset: 0 }}
            animate={{ strokeDashoffset: 100 - percentage }}
            transition={{ duration: 0.5 }}
          />
        </svg>
        <div className={`absolute inset-0 flex items-center justify-center text-2xl font-bold ${
          isUrgent ? 'text-red-500' : 'text-purple-600'
        }`}>
          {timeLeft}
        </div>
      </div>
    </div>
  );
};