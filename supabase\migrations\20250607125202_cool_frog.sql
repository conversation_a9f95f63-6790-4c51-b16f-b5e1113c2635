/*
  # Fix User Table RLS Policies

  1. Security Updates
    - Update RLS policies for users table to handle signup properly
    - Ensure proper user creation flow
    - Add policy for public user creation during signup

  2. Changes
    - Drop existing problematic policies
    - Create new policies that allow proper user signup
    - Ensure users can be created during the auth signup process
*/

-- Drop existing policies that might be causing issues
DROP POLICY IF EXISTS "Users can insert own profile during signup" ON users;
DROP POLICY IF EXISTS "Users can read own data" ON users;
DROP POLICY IF EXISTS "Users can update own data" ON users;
DROP POLICY IF EXISTS "Allow reading public user profiles" ON users;

-- Create new policies for proper user management
CREATE POLICY "Enable insert for authenticated users during signup"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable read access for own user data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Enable update for own user data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Allow reading public user profiles (for leaderboards, etc.)
CREATE POLICY "Enable read access for public user profiles"
  ON users
  FOR SELECT
  TO authenticated
  USING (true);

-- Create a function to handle user creation after signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (id, email, username, profile_picture_url)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', split_part(new.email, '@', 1)),
    new.raw_user_meta_data->>'avatar_url'
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profile after auth signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();