import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Trophy, 
  Target, 
  Zap, 
  Award,
  Edit3,
  Save,
  X,
  Coins,
  TrendingUp,
  Clock
} from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

export const ProfilePage: React.FC = () => {
  const { user, updateProfile } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    upi_id: user?.upi_id || ''
  });

  if (!user) return null;

  const handleSave = async () => {
    try {
      await updateProfile(editData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleCancel = () => {
    setEditData({
      username: user.username,
      email: user.email,
      upi_id: user.upi_id || ''
    });
    setIsEditing(false);
  };

  const badges = [
    { 
      id: 'first_win', 
      name: 'First Victory', 
      icon: '🏆', 
      earned: user.matches_won > 0,
      description: 'Win your first battle'
    },
    { 
      id: 'quiz_master', 
      name: 'Quiz Master', 
      icon: '🎓', 
      earned: user.total_score > 500,
      description: 'Score 500+ total points'
    },
    { 
      id: 'speed_demon', 
      name: 'Speed Demon', 
      icon: '⚡', 
      earned: user.matches_played > 10,
      description: 'Play 10+ matches'
    },
    { 
      id: 'coin_collector', 
      name: 'Coin Collector', 
      icon: '🪙', 
      earned: user.total_coins > 200,
      description: 'Collect 200+ coins'
    },
    { 
      id: 'unbeatable', 
      name: 'Unbeatable', 
      icon: '👑', 
      earned: user.matches_won > 20,
      description: 'Win 20+ battles'
    },
    { 
      id: 'daily_champion', 
      name: 'Daily Champion', 
      icon: '🌟', 
      earned: user.daily_score > 50,
      description: 'Score 50+ points today'
    }
  ];

  const stats = [
    { label: 'Total Score', value: user.total_score.toLocaleString(), icon: Trophy, color: 'text-yellow-600' },
    { label: 'Matches Played', value: user.matches_played, icon: Target, color: 'text-blue-600' },
    { label: 'Matches Won', value: user.matches_won, icon: Award, color: 'text-green-600' },
    { label: 'Win Rate', value: `${user.matches_played > 0 ? Math.round((user.matches_won / user.matches_played) * 100) : 0}%`, icon: TrendingUp, color: 'text-purple-600' }
  ];

  const recentMatches = [
    { result: 'Win', opponent: 'MathWizard', date: '2024-01-15', score: '8-6' },
    { result: 'Loss', opponent: 'CodeNinja', date: '2024-01-14', score: '5-7' },
    { result: 'Win', opponent: 'CinemaFan', date: '2024-01-13', score: '9-4' },
    { result: 'Win', opponent: 'QuizMaster', date: '2024-01-12', score: '7-5' },
    { result: 'Loss', opponent: 'BrainStorm', date: '2024-01-11', score: '4-8' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-20 pb-24 md:pb-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="relative inline-block">
            <div className="w-32 h-32 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-xl">
              <User className="w-16 h-16 text-white" />
            </div>
            <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">
                {Math.floor(user.total_score / 100) + 1}
              </span>
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{user.username}</h1>
          <p className="text-gray-600 mb-4">Level {Math.floor(user.total_score / 100) + 1} • University of California, Berkeley</p>
          
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full shadow-lg">
              <Coins className="w-5 h-5" />
              <span className="font-bold">{user.total_coins}</span>
            </div>
            <Button
              onClick={() => setIsEditing(true)}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Edit3 className="w-4 h-4" />
              <span>Edit Profile</span>
            </Button>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.label} className="p-4 text-center">
                <Icon className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
                <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </Card>
            );
          })}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Badges Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Badges</h3>
              <div className="grid grid-cols-3 gap-4">
                {badges.map((badge) => (
                  <motion.div
                    key={badge.id}
                    whileHover={{ scale: 1.05 }}
                    className={`relative p-4 rounded-xl text-center transition-all duration-200 ${
                      badge.earned 
                        ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-2 border-yellow-200' 
                        : 'bg-gray-50 border-2 border-gray-200 opacity-60'
                    }`}
                  >
                    <div className="text-3xl mb-2">{badge.icon}</div>
                    <div className="text-xs font-semibold text-gray-900 mb-1">{badge.name}</div>
                    <div className="text-xs text-gray-600">{badge.description}</div>
                    {badge.earned && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </Card>
          </motion.div>

          {/* Personal Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Personal Information</h3>
                {isEditing && (
                  <div className="flex space-x-2">
                    <Button onClick={handleSave} size="sm" className="flex items-center space-x-1">
                      <Save className="w-4 h-4" />
                      <span>Save</span>
                    </Button>
                    <Button onClick={handleCancel} variant="outline" size="sm" className="flex items-center space-x-1">
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <div className="text-sm text-gray-600">Email</div>
                    {isEditing ? (
                      <input
                        type="email"
                        value={editData.email}
                        onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    ) : (
                      <div className="font-medium text-gray-900">{user.email}</div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-purple-600" />
                  <div className="flex-1">
                    <div className="text-sm text-gray-600">Username</div>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.username}
                        onChange={(e) => setEditData({ ...editData, username: e.target.value })}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    ) : (
                      <div className="font-medium text-gray-900">{user.username}</div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-green-600" />
                  <div className="flex-1">
                    <div className="text-sm text-gray-600">UPI ID</div>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.upi_id}
                        onChange={(e) => setEditData({ ...editData, upi_id: e.target.value })}
                        placeholder="your-upi@paytm"
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    ) : (
                      <div className="font-medium text-gray-900">{user.upi_id || 'Not set'}</div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-orange-600" />
                  <div className="flex-1">
                    <div className="text-sm text-gray-600">Joined</div>
                    <div className="font-medium text-gray-900">
                      {new Date(user.created_at).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Match History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <Card className="p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Match History</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Result</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Opponent</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Score</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {recentMatches.map((match, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          match.result === 'Win' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {match.result}
                        </span>
                      </td>
                      <td className="py-3 px-4 font-medium text-gray-900">{match.opponent}</td>
                      <td className="py-3 px-4 text-gray-600">{match.score}</td>
                      <td className="py-3 px-4 text-gray-600">{match.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </motion.div>

        {/* Rank Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <Card className="p-6 bg-gradient-to-r from-teal-500 to-blue-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-2">Your Rank</h3>
                <p className="text-teal-100 mb-4">
                  You're currently ranked #{user.current_rank || 'Unranked'} on the leaderboard. Keep playing to climb higher!
                </p>
                <div className="flex space-x-4">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-teal-600">
                    Play Now
                  </Button>
                  <Button variant="ghost" className="text-white hover:bg-white hover:bg-opacity-20">
                    View Leaderboard
                  </Button>
                </div>
              </div>
              <div className="hidden md:block">
                <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <Trophy className="w-12 h-12 text-yellow-300" />
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};