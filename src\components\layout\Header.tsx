import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, User, LogOut } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';

export const Header: React.FC = () => {
  const { user, signOut } = useAuthStore();

  // Header is now replaced by Navbar component
  // This component is kept for backward compatibility but won't be used
  if (!user) return null;

  return null;
};