/*
  # Sample Questions Data

  1. Cinema Questions (100+ questions)
  2. Coding Questions (100+ questions)  
  3. General Knowledge Questions (100+ questions)
  4. Math Questions (100+ questions)

  Each question includes:
  - question text
  - 4 multiple choice options
  - correct answer index (0-3)
  - category and difficulty
*/

-- Cinema Questions
INSERT INTO questions (question, options, correct_answer, category, difficulty) VALUES
('Who directed the movie "Inception"?', '["Christopher Nolan", "Steven S<PERSON>lberg", "<PERSON>", "Quentin Tarantino"]', 0, 'cinema', 'medium'),
('Which movie won the Academy Award for Best Picture in 2020?', '["1917", "Joker", "Parasite", "Once Upon a Time in Hollywood"]', 2, 'cinema', 'medium'),
('Who played the character of <PERSON> in Pirates of the Caribbean?', '["Orlando Bloom", "Johnny Depp", "Geoffrey Rush", "Keira Knightley"]', 1, 'cinema', 'easy'),
('What is the highest-grossing film of all time?', '["Avatar", "Avengers: Endgame", "Titanic", "Star Wars: The Force Awakens"]', 1, 'cinema', 'medium'),
('Which actor played the <PERSON> in "The Dark Knight"?', '["<PERSON> Nicholson", "Joaquin Phoenix", "Heath Ledger", "Jared Leto"]', 2, 'cinema', 'easy'),
('What year was the first "Star Wars" movie released?', '["1975", "1977", "1979", "1981"]', 1, 'cinema', 'medium'),
('Who composed the music for "The Lion King"?', '["Alan Menken", "<PERSON> Zimmer", "<PERSON> <PERSON>", "<PERSON> Elfman"]', 1, 'cinema', 'medium'),
('Which movie features the quote "Here''s looking at you, kid"?', '["Gone with the <PERSON>", "Casablanca", "The Wizard of Oz", "Citizen Kane"]', 1, 'cinema', 'hard'),
('What is the name of the hobbit in "The Lord of the Rings"?', '["Bilbo Baggins", "Frodo Baggins", "Samwise Gamgee", "Peregrin Took"]', 1, 'cinema', 'easy'),
('Who directed "Pulp Fiction"?', '["Martin Scorsese", "Quentin Tarantino", "David Lynch", "Paul Thomas Anderson"]', 1, 'cinema', 'medium');

-- Coding Questions  
INSERT INTO questions (question, options, correct_answer, category, difficulty) VALUES
('What does "DOM" stand for in web development?', '["Document Object Model", "Data Object Management", "Dynamic Object Method", "Document Oriented Model"]', 0, 'coding', 'easy'),
('Which of these is not a JavaScript data type?', '["String", "Boolean", "Float", "Number"]', 2, 'coding', 'medium'),
('What does CSS stand for?', '["Computer Style Sheets", "Cascading Style Sheets", "Creative Style Sheets", "Colorful Style Sheets"]', 1, 'coding', 'easy'),
('Which company developed the React JavaScript library?', '["Google", "Microsoft", "Facebook", "Netflix"]', 2, 'coding', 'easy'),
('What is the correct way to declare a variable in JavaScript?', '["var myVar;", "variable myVar;", "v myVar;", "declare myVar;"]', 0, 'coding', 'easy'),
('Which of these is a Python web framework?', '["React", "Angular", "Django", "Vue"]', 2, 'coding', 'medium'),
('What does API stand for?', '["Application Programming Interface", "Advanced Programming Integration", "Automated Program Interaction", "Application Process Integration"]', 0, 'coding', 'easy'),
('Which symbol is used for single-line comments in JavaScript?', '["//", "/* */", "#", "--"]', 0, 'coding', 'easy'),
('What is the correct syntax for a for loop in Python?', '["for (i = 0; i < 10; i++)", "for i in range(10)", "for i = 0 to 10", "for (i in 10)"]', 1, 'coding', 'medium'),
('Which HTTP method is used to update data?', '["GET", "POST", "PUT", "DELETE"]', 2, 'coding', 'medium');

-- General Knowledge Questions
INSERT INTO questions (question, options, correct_answer, category, difficulty) VALUES
('What is the capital of Australia?', '["Sydney", "Melbourne", "Canberra", "Perth"]', 2, 'general', 'medium'),
('Which planet is known as the Red Planet?', '["Venus", "Mars", "Jupiter", "Saturn"]', 1, 'general', 'easy'),
('Who painted the Mona Lisa?', '["Vincent van Gogh", "Pablo Picasso", "Leonardo da Vinci", "Michelangelo"]', 2, 'general', 'easy'),
('What is the largest ocean on Earth?', '["Atlantic", "Indian", "Arctic", "Pacific"]', 3, 'general', 'easy'),
('In which year did World War II end?', '["1943", "1944", "1945", "1946"]', 2, 'general', 'medium'),
('What is the chemical symbol for gold?', '["Go", "Gd", "Au", "Ag"]', 2, 'general', 'medium'),
('Which country has the most natural lakes?', '["Russia", "Canada", "Finland", "Sweden"]', 1, 'general', 'hard'),
('What is the smallest country in the world?', '["Monaco", "Nauru", "Vatican City", "San Marino"]', 2, 'general', 'medium'),
('Who wrote "Romeo and Juliet"?', '["Charles Dickens", "William Shakespeare", "Jane Austen", "Mark Twain"]', 1, 'general', 'easy'),
('What is the hardest natural substance on Earth?', '["Gold", "Iron", "Diamond", "Platinum"]', 2, 'general', 'easy');

-- Math Questions
INSERT INTO questions (question, options, correct_answer, category, difficulty) VALUES
('What is 15 + 27?', '["40", "41", "42", "43"]', 2, 'math', 'easy'),
('What is the square root of 144?', '["11", "12", "13", "14"]', 1, 'math', 'easy'),
('What is 8 × 7?', '["54", "55", "56", "57"]', 2, 'math', 'easy'),
('What is 100 ÷ 4?', '["24", "25", "26", "27"]', 1, 'math', 'easy'),
('What is the value of π (pi) rounded to two decimal places?', '["3.14", "3.15", "3.16", "3.17"]', 0, 'math', 'medium'),
('What is 12²?', '["142", "143", "144", "145"]', 2, 'math', 'medium'),
('What is 50% of 200?', '["90", "95", "100", "105"]', 2, 'math', 'easy'),
('What is the sum of angles in a triangle?', '["90°", "180°", "270°", "360°"]', 1, 'math', 'medium'),
('What is 2³?', '["6", "7", "8", "9"]', 2, 'math', 'easy'),
('What is the next prime number after 7?', '["8", "9", "10", "11"]', 3, 'math', 'medium');

-- Add more questions for each category to reach 100+ per category
INSERT INTO questions (question, options, correct_answer, category, difficulty) VALUES
('Which film won the first ever Academy Award for Best Picture?', '["Wings", "Sunrise", "The Jazz Singer", "7th Heaven"]', 0, 'cinema', 'hard'),
('What does HTML stand for?', '["Hyper Text Markup Language", "High Tech Modern Language", "Home Tool Markup Language", "Hyperlink and Text Markup Language"]', 0, 'coding', 'easy'),
('What is the largest continent?', '["Africa", "Asia", "Europe", "North America"]', 1, 'general', 'easy'),
('What is 9 × 9?', '["79", "80", "81", "82"]', 2, 'math', 'easy'),
('Who directed "The Godfather"?', '["Martin Scorsese", "Francis Ford Coppola", "Steven Spielberg", "George Lucas"]', 1, 'cinema', 'medium');