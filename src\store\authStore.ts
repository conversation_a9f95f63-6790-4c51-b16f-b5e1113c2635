import { create } from 'zustand';
import { supabase, db } from '../lib/supabase';
import { User } from '../types';
import toast from 'react-hot-toast';

interface AuthState {
  user: User | null;
  loading: boolean;
  userStatsSubscription: any;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, username: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  initialize: () => Promise<void>;
  refreshUserStats: () => Promise<void>;
  setupUserStatsSubscription: (userId: string) => void;
  cleanupUserStatsSubscription: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: true,
  userStatsSubscription: null,

  refreshUserStats: async () => {
    const { user } = get();
    if (!user) return;

    try {
      const updatedProfile = await db.getUserProfile(user.id);
      set({ user: updatedProfile });
    } catch (error) {
      console.error('Error refreshing user stats:', error);
    }
  },

  setupUserStatsSubscription: (userId: string) => {
    const { userStatsSubscription } = get();
    
    // Clean up existing subscription
    if (userStatsSubscription) {
      try {
        supabase.removeChannel(userStatsSubscription);
      } catch (error) {
        console.error('Error removing existing user stats subscription:', error);
      }
    }

    // Set up new subscription
    const subscription = db.subscribeToUserStats(userId, (payload) => {
      const updatedUser = payload.new;
      set({ user: updatedUser });
      
      // Show toast for significant changes
      if (payload.old && updatedUser.total_coins > payload.old.total_coins) {
        const coinsEarned = updatedUser.total_coins - payload.old.total_coins;
        toast.success(`+${coinsEarned} coins earned!`);
      }
    });

    set({ userStatsSubscription: subscription });
  },

  cleanupUserStatsSubscription: () => {
    const { userStatsSubscription } = get();
    if (userStatsSubscription) {
      try {
        supabase.removeChannel(userStatsSubscription);
        set({ userStatsSubscription: null });
      } catch (error) {
        console.error('Error cleaning up user stats subscription:', error);
      }
    }
  },

  signIn: async (email: string, password: string) => {
    try {
      // Ensure we have a valid session before making DB calls
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.user && data.session) {
        // Wait a moment for the session to be fully established
        await new Promise(resolve => setTimeout(resolve, 500));
        
        try {
          // Ensure user profile exists in both tables
          await db.ensureUserProfileExists(data.user.id, {
            email: data.user.email,
            username: data.user.email?.split('@')[0] || 'User'
          });

          // Get user profile using the authenticated session
          const profile = await db.getUserProfile(data.user.id);
          set({ user: profile });
          
          // Set up real-time user stats subscription
          get().setupUserStatsSubscription(data.user.id);
          
          toast.success('Welcome back!');
        } catch (profileError: any) {
          console.error('Profile fetch error:', profileError);
          
          // If profile doesn't exist (PGRST116 = no rows returned), create it
          if (profileError.code === 'PGRST116') {
            try {
              const newProfile = await db.createUserProfile(data.user.id, {
                email: data.user.email,
                username: data.user.email?.split('@')[0] || 'User',
                total_coins: 100,
                total_score: 0,
                daily_score: 0,
                weekly_score: 0,
                matches_played: 0,
                matches_won: 0
              });
              set({ user: newProfile });
              get().setupUserStatsSubscription(data.user.id);
              toast.success('Welcome back!');
            } catch (createError: any) {
              console.error('Failed to create profile during sign in:', createError);
              toast.error('Sign in successful but profile setup failed. Please try again.');
            }
          } else {
            throw profileError;
          }
        }
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      let errorMessage = 'Failed to sign in. Please try again.';
      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials.';
      } else if (error.message?.includes('Email not confirmed')) {
        errorMessage = 'Please check your email and confirm your account.';
      }
      
      toast.error(errorMessage);
      throw error;
    }
  },

  signUp: async (email: string, password: string, username: string) => {
    try {
      // Sign up the user with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      });

      if (error) throw error;

      if (data.user) {
        // Wait for the trigger to potentially create the user profile
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if we have a session (auto-login after signup)
        const { data: sessionData } = await supabase.auth.getSession();
        
        if (sessionData.session) {
          try {
            // Ensure user profile exists in both tables
            await db.ensureUserProfileExists(data.user.id, {
              email,
              username,
              total_coins: 100,
              total_score: 0,
              daily_score: 0,
              weekly_score: 0,
              matches_played: 0,
              matches_won: 0
            });

            // Try to get the user profile (might be created by trigger)
            const profile = await db.getUserProfile(data.user.id);
            set({ user: profile });
            
            // Set up real-time user stats subscription
            get().setupUserStatsSubscription(data.user.id);
            
            toast.success('Account created successfully! Welcome to QuizBattle Pro!');
          } catch (profileError: any) {
            console.error('Profile fetch error after signup:', profileError);
            
            // If profile doesn't exist, create it manually
            if (profileError.code === 'PGRST116') {
              try {
                const newProfile = await db.createUserProfile(data.user.id, {
                  email,
                  username,
                  total_coins: 100,
                  total_score: 0,
                  daily_score: 0,
                  weekly_score: 0,
                  matches_played: 0,
                  matches_won: 0
                });
                set({ user: newProfile });
                get().setupUserStatsSubscription(data.user.id);
                toast.success('Account created successfully! Welcome to QuizBattle Pro!');
              } catch (createError: any) {
                console.error('Failed to create user profile manually:', createError);
                toast.error('Account created but profile setup failed. Please try signing in.');
                throw createError;
              }
            } else {
              throw profileError;
            }
          }
        } else {
          // No auto-login, user needs to confirm email
          toast.success('Account created! Please check your email to confirm your account.');
        }
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      
      // Provide user-friendly error messages
      let errorMessage = 'Failed to create account. Please try again.';
      
      if (error.message?.includes('already registered') || error.message?.includes('already been registered')) {
        errorMessage = 'This email is already registered. Please sign in instead.';
      } else if (error.message?.includes('password')) {
        errorMessage = 'Password must be at least 6 characters long.';
      } else if (error.message?.includes('email')) {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.message?.includes('row-level security')) {
        errorMessage = 'Account setup failed due to security policy. Please contact support.';
      } else if (error.code === '42501') {
        errorMessage = 'Account setup failed due to permissions. Please try again or contact support.';
      }
      
      toast.error(errorMessage);
      throw error;
    }
  },

  signOut: async () => {
    try {
      // Clean up subscriptions before signing out
      get().cleanupUserStatsSubscription();
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      set({ user: null });
      toast.success('Signed out successfully');
    } catch (error: any) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  },

  updateProfile: async (updates: Partial<User>) => {
    try {
      const { user } = get();
      if (!user) throw new Error('No user found');

      const updatedProfile = await db.updateUserProfile(user.id, {
        ...updates,
        updated_at: new Date().toISOString()
      });
      set({ user: updatedProfile });
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Update profile error:', error);
      toast.error('Failed to update profile');
      throw error;
    }
  },

  initialize: async () => {
    try {
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Session error:', sessionError);
        return;
      }
      
      if (session?.user) {
        try {
          // Ensure user profile exists in both tables
          await db.ensureUserProfileExists(session.user.id, {
            email: session.user.email,
            username: session.user.email?.split('@')[0] || 'User'
          });

          const profile = await db.getUserProfile(session.user.id);
          set({ user: profile });
          
          // Set up real-time user stats subscription
          get().setupUserStatsSubscription(session.user.id);
        } catch (error: any) {
          console.error('Error fetching user profile during initialization:', error);
          
          // If profile doesn't exist, create it
          if (error.code === 'PGRST116') {
            try {
              const newProfile = await db.createUserProfile(session.user.id, {
                email: session.user.email,
                username: session.user.email?.split('@')[0] || 'User',
                total_coins: 100,
                total_score: 0,
                daily_score: 0,
                weekly_score: 0,
                matches_played: 0,
                matches_won: 0
              });
              set({ user: newProfile });
              get().setupUserStatsSubscription(session.user.id);
            } catch (createError) {
              console.error('Failed to create user profile during initialization:', createError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
    } finally {
      set({ loading: false });
    }

    // Listen for auth changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session?.user?.id);
      
      if (event === 'SIGNED_OUT') {
        get().cleanupUserStatsSubscription();
        set({ user: null });
      } else if (session?.user && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
        try {
          // Wait a moment for the session to be fully established
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Ensure user profile exists in both tables
          await db.ensureUserProfileExists(session.user.id, {
            email: session.user.email,
            username: session.user.email?.split('@')[0] || 'User'
          });

          const profile = await db.getUserProfile(session.user.id);
          set({ user: profile });
          
          // Set up real-time user stats subscription
          get().setupUserStatsSubscription(session.user.id);
        } catch (error: any) {
          console.error('Error fetching user profile on auth change:', error);
          
          // Try to create profile if it doesn't exist
          if (error.code === 'PGRST116') {
            try {
              const newProfile = await db.createUserProfile(session.user.id, {
                email: session.user.email,
                username: session.user.email?.split('@')[0] || 'User',
                total_coins: 100,
                total_score: 0,
                daily_score: 0,
                weekly_score: 0,
                matches_played: 0,
                matches_won: 0
              });
              set({ user: newProfile });
              get().setupUserStatsSubscription(session.user.id);
            } catch (createError) {
              console.error('Failed to create user profile on auth change:', createError);
            }
          }
        }
      }
    });
  },
}));