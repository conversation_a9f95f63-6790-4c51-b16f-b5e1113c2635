import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, Coins, Gift, Star, Zap, Target } from 'lucide-react';

export const RewardsSection: React.FC = () => {
  const rewards = [
    {
      icon: Trophy,
      title: 'Daily Victories',
      amount: '₹20-50',
      description: 'Win battles and earn instant rewards',
      color: 'from-yellow-400 to-orange-500',
    },
    {
      icon: Star,
      title: 'Weekly Champions',
      amount: '₹100-500',
      description: 'Top leaderboard positions get bonus rewards',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: Zap,
      title: 'Streak Bonuses',
      amount: '₹10-100',
      description: 'Maintain winning streaks for multiplier rewards',
      color: 'from-blue-500 to-teal-500',
    },
    {
      icon: Target,
      title: 'Perfect Scores',
      amount: '₹25-75',
      description: 'Get all 10 questions right for bonus cash',
      color: 'from-green-500 to-emerald-500',
    },
  ];

  const achievements = [
    { name: 'First Victory', reward: '₹10', icon: '🏆' },
    { name: 'Quiz Master', reward: '₹50', icon: '🎓' },
    { name: 'Speed Demon', reward: '₹25', icon: '⚡' },
    { name: 'Category Expert', reward: '₹75', icon: '🌟' },
    { name: 'Unbeatable', reward: '₹100', icon: '👑' },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-10 right-10 w-32 h-32 bg-yellow-400 bg-opacity-10 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-10 left-10 w-48 h-48 bg-green-400 bg-opacity-10 rounded-full blur-xl"
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Earn Real <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">Money</span> Playing
          </h2>
          <p className="text-xl text-purple-100 max-w-3xl mx-auto">
            Turn your quiz skills into cash rewards. The more you play and win, the more you earn!
          </p>
        </motion.div>

        {/* Main Earning Potential */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-3xl p-8 md:p-12 shadow-2xl">
            <Coins className="w-20 h-20 mx-auto mb-6 text-yellow-900" />
            <h3 className="text-4xl md:text-6xl font-bold text-yellow-900 mb-4">
              ₹100+
            </h3>
            <p className="text-xl md:text-2xl text-yellow-800 font-semibold mb-4">
              Daily Earning Potential
            </p>
            <p className="text-yellow-700 max-w-2xl mx-auto">
              Top players consistently earn ₹100+ daily through victories, streaks, and leaderboard positions
            </p>
          </div>
        </motion.div>

        {/* Reward Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {rewards.map((reward, index) => {
            const Icon = reward.icon;
            return (
              <motion.div
                key={reward.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-center border border-white border-opacity-20"
              >
                <motion.div
                  whileHover={{ rotate: 10 }}
                  className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${reward.color} flex items-center justify-center`}
                >
                  <Icon className="w-8 h-8 text-white" />
                </motion.div>
                <h4 className="text-xl font-bold mb-2">{reward.title}</h4>
                <div className="text-2xl font-bold text-yellow-300 mb-3">{reward.amount}</div>
                <p className="text-purple-100 text-sm">{reward.description}</p>
              </motion.div>
            );
          })}
        </div>

        {/* Achievement Badges */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl p-8 mb-16"
        >
          <h3 className="text-3xl font-bold text-center mb-8">
            Achievement <span className="text-yellow-300">Rewards</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.name}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.1 }}
                className="text-center"
              >
                <div className="text-4xl mb-2">{achievement.icon}</div>
                <h4 className="font-semibold mb-1">{achievement.name}</h4>
                <div className="text-yellow-300 font-bold">{achievement.reward}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* How Payments Work */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-green-600 to-teal-600 rounded-3xl p-8 text-center"
        >
          <Gift className="w-16 h-16 mx-auto mb-6 text-green-100" />
          <h3 className="text-3xl font-bold mb-4">Instant Payouts</h3>
          <p className="text-xl text-green-100 mb-6 max-w-3xl mx-auto">
            Withdraw your earnings instantly to your UPI, bank account, or digital wallet. Minimum withdrawal is just ₹10!
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white bg-opacity-20 rounded-xl p-4">
              <div className="text-2xl mb-2">💳</div>
              <div className="font-semibold">UPI Transfer</div>
              <div className="text-sm text-green-100">Instant payments</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-xl p-4">
              <div className="text-2xl mb-2">🏦</div>
              <div className="font-semibold">Bank Transfer</div>
              <div className="text-sm text-green-100">1-2 business days</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-xl p-4">
              <div className="text-2xl mb-2">📱</div>
              <div className="font-semibold">Digital Wallets</div>
              <div className="text-sm text-green-100">PayTM, PhonePe, GPay</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};