import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Award, TrendingUp } from 'lucide-react';
import { QuizCategory } from '../../types';
import { CategoryCard } from './CategoryCard';
import { GameModeSelector } from './GameModeSelector';
import { Card } from '../ui/Card';

export const HomePage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<QuizCategory | null>(null);

  const categories = Object.values(QuizCategory);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Promotional Banner */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white rounded-xl p-6 mb-8 text-center shadow-lg"
        >
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Trophy className="w-8 h-8" />
            <h2 className="text-2xl font-bold">🏆 Earn ₹100 daily by playing quizzes!</h2>
          </div>
          <p className="text-lg opacity-90">
            Challenge players worldwide and climb the leaderboard
          </p>
        </motion.div>

        {!selectedCategory ? (
          <>
            {/* Category Selection */}
            <div className="text-center mb-8">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl font-bold text-gray-900 mb-4"
              >
                Choose Your Battle Category
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-xl text-gray-600"
              >
                Select a category to start your quiz battle journey
              </motion.p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {categories.map((category, index) => (
                <motion.div
                  key={category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <CategoryCard
                    category={category}
                    onSelect={setSelectedCategory}
                  />
                </motion.div>
              ))}
            </div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              <Card className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Daily Challenges
                </h3>
                <p className="text-gray-600">
                  Complete daily quizzes to earn rewards and climb leaderboards
                </p>
              </Card>

              <Card className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Achievements
                </h3>
                <p className="text-gray-600">
                  Unlock badges and achievements as you progress through levels
                </p>
              </Card>

              <Card className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Global Rankings
                </h3>
                <p className="text-gray-600">
                  Compete with players worldwide and track your progress
                </p>
              </Card>
            </motion.div>
          </>
        ) : (
          <GameModeSelector
            selectedCategory={selectedCategory}
            onBack={() => setSelectedCategory(null)}
          />
        )}
      </div>
    </div>
  );
};