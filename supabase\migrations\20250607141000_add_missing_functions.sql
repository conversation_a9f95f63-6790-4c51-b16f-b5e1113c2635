/*
  # Add Missing Database Functions for Real-time Battle System

  1. User Stats Functions
    - Function to update user stats after match completion
    - <PERSON><PERSON> wins, losses, coins, and experience

  2. Additional Tables
    - coin_transactions table for tracking coin history
    - game_history table for match history

  3. Performance Optimizations
    - Add indexes for better query performance
    - Optimize real-time subscriptions
*/

-- Create coin_transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.coin_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) NOT NULL,
  amount integer NOT NULL,
  transaction_type text NOT NULL,
  description text,
  match_id uuid REFERENCES public.matches(id),
  created_at timestamptz DEFAULT now()
);

-- Create game_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.game_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  match_id uuid REFERENCES public.matches(id) NOT NULL,
  user_id uuid REFERENCES public.profiles(id) NOT NULL,
  opponent_id uuid REFERENCES public.profiles(id),
  opponent_name text NOT NULL,
  user_score integer NOT NULL DEFAULT 0,
  opponent_score integer NOT NULL DEFAULT 0,
  result text NOT NULL, -- 'win', 'loss', 'draw'
  coins_earned integer NOT NULL DEFAULT 0,
  experience_gained integer NOT NULL DEFAULT 0,
  match_duration text,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE public.coin_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for coin_transactions
CREATE POLICY "Users can read own transactions" ON public.coin_transactions FOR SELECT TO authenticated 
  USING (auth.uid() = user_id);
CREATE POLICY "System can insert transactions" ON public.coin_transactions FOR INSERT TO authenticated 
  WITH CHECK (true); -- Allow system to insert transactions

-- RLS Policies for game_history
CREATE POLICY "Users can read own game history" ON public.game_history FOR SELECT TO authenticated 
  USING (auth.uid() = user_id);
CREATE POLICY "System can insert game history" ON public.game_history FOR INSERT TO authenticated 
  WITH CHECK (true); -- Allow system to insert game history

-- Create function to update user stats after match
CREATE OR REPLACE FUNCTION public.update_user_stats_after_match(
  user_id UUID,
  won BOOLEAN,
  coins_earned INTEGER,
  score_earned INTEGER
)
RETURNS void AS $$
BEGIN
  -- Update users table
  UPDATE public.users 
  SET 
    total_coins = total_coins + coins_earned,
    total_score = total_score + score_earned,
    daily_score = daily_score + score_earned,
    weekly_score = weekly_score + score_earned,
    matches_played = matches_played + 1,
    matches_won = CASE WHEN won THEN matches_won + 1 ELSE matches_won END,
    updated_at = NOW()
  WHERE id = user_id;
  
  -- Update profiles table
  UPDATE public.profiles 
  SET 
    total_games = total_games + 1,
    total_wins = CASE WHEN won THEN total_wins + 1 ELSE total_wins END
  WHERE id = user_id;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to update user stats for user %: %', user_id, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coin_transactions_user_id ON public.coin_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_created_at ON public.coin_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_game_history_user_id ON public.game_history(user_id);
CREATE INDEX IF NOT EXISTS idx_game_history_match_id ON public.game_history(match_id);
CREATE INDEX IF NOT EXISTS idx_game_history_created_at ON public.game_history(created_at);

-- Add indexes for matches table real-time queries
CREATE INDEX IF NOT EXISTS idx_matches_current_question_start_time ON public.matches(current_question_start_time);
CREATE INDEX IF NOT EXISTS idx_matches_player1_answers ON public.matches USING GIN(player1_answers);
CREATE INDEX IF NOT EXISTS idx_matches_player2_answers ON public.matches USING GIN(player2_answers);

-- Create function to sync question timing across players
CREATE OR REPLACE FUNCTION public.sync_question_timing(
  match_id UUID,
  question_start_time TIMESTAMPTZ
)
RETURNS void AS $$
BEGIN
  UPDATE public.matches 
  SET current_question_start_time = question_start_time
  WHERE id = match_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get active queue count for a category
CREATE OR REPLACE FUNCTION public.get_active_queue_count(category_name TEXT)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.matchmaking_queue 
    WHERE category = category_name 
    AND is_active = true 
    AND last_active_at > NOW() - INTERVAL '2 minutes'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for daily leaderboard if it doesn't exist
CREATE OR REPLACE VIEW public.daily_leaderboard AS
SELECT 
  u.id,
  u.username,
  u.daily_score,
  u.profile_picture_url,
  ROW_NUMBER() OVER (ORDER BY u.daily_score DESC) as rank
FROM public.users u
WHERE u.daily_score > 0
ORDER BY u.daily_score DESC
LIMIT 100;

-- Create view for weekly leaderboard if it doesn't exist
CREATE OR REPLACE VIEW public.weekly_leaderboard AS
SELECT 
  u.id,
  u.username,
  u.weekly_score,
  u.profile_picture_url,
  ROW_NUMBER() OVER (ORDER BY u.weekly_score DESC) as rank
FROM public.users u
WHERE u.weekly_score > 0
ORDER BY u.weekly_score DESC
LIMIT 100;

-- Grant necessary permissions
GRANT SELECT ON public.daily_leaderboard TO authenticated;
GRANT SELECT ON public.weekly_leaderboard TO authenticated;
