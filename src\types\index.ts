export interface User {
  id: string;
  username: string;
  email: string;
  profile_picture_url?: string;
  total_coins: number;
  total_score: number;
  daily_score: number;
  weekly_score: number;
  matches_played: number;
  matches_won: number;
  upi_id?: string;
  created_at: string;
  updated_at: string;
  current_rank?: number;
}

export interface Question {
  id: string;
  category: string;
  question: string;
  options: string[];
  correct_answer: string;
  difficulty: string;
  is_active: boolean;
  created_at: string;
}

export enum QuizCategory {
  CINEMA = 'cinema',
  CODING = 'coding',
  GENERAL = 'GK',
  MATH = 'math'
}

export interface Battle {
  id: string;
  player1_id: string;
  player2_id?: string;
  status: string;
  questions_assigned: string[];
  player1_score: number;
  player2_score: number;
  winner_id?: string;
  match_type: string;
  start_time?: string;
  end_time?: string;
  is_bot_match: boolean;
  is_solo_match: boolean;
  created_at: string;
  player1?: User;
  player2?: User;
}

export interface GameState {
  currentQuestion: number;
  timeLeft: number;
  score: number;
  opponentScore: number;
  isAnswered: boolean;
  selectedAnswer?: number;
  questions: Question[];
}

export interface LeaderboardEntry {
  id: string;
  user_id: string;
  username: string;
  score: number;
  category: QuizCategory;
  period: 'daily' | 'weekly';
  rank: number;
}

export interface MatchmakingQueue {
  id: string;
  user_id: string;
  category: QuizCategory;
  created_at: string;
}