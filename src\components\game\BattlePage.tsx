import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Crown, Zap, CheckCircle, XCircle, Timer, ArrowLeft, Users, Wifi, WifiOff } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import Confetti from 'react-confetti';
import { db } from '../../lib/supabase';
import toast from 'react-hot-toast';

export const BattlePage: React.FC = () => {
  const { matchId } = useParams<{ matchId: string }>();
  const navigate = useNavigate();
  const [showResults, setShowResults] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [loading, setLoading] = useState(true);
  const [opponent, setOpponent] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(true);
  
  const { user } = useAuthStore();
  const {
    battle,
    currentQuestionData,
    currentQuestion,
    score,
    opponentScore,
    isAnswered,
    selectedAnswer,
    syncedTimeLeft,
    answerQuestion,
    nextQuestion,
    endBattle,
    resetGame,
    cleanupSubscriptions,
    setBattle,
    setQuestions,
    startQuestionTimer,
    stopQuestionTimer,
    handleDisconnection,
    reconnectToBattle
  } = useGameStore();

  // Load battle data on mount
  useEffect(() => {
    const loadBattle = async () => {
      if (!matchId || !user) return;
      
      try {
        setLoading(true);
        const { match, questions } = await db.getMatchWithQuestions(matchId);
        
        // Verify user is part of this match
        if (match.player1_id !== user.id && match.player2_id !== user.id) {
          toast.error('You are not part of this battle');
          navigate('/home');
          return;
        }
        
        setBattle(match);
        setQuestions(questions);
        
        // Start the question timer for the first question
        if (!isAnswered && questions.length > 0) {
          startQuestionTimer();
        }
        
        // Get opponent info
        const opponentId = match.player1_id === user.id ? match.player2_id : match.player1_id;
        if (opponentId && !match.is_bot_match) {
          try {
            const opponentData = await db.getUserProfile(opponentId);
            setOpponent(opponentData);
          } catch (error) {
            console.error('Error loading opponent data:', error);
          }
        }
        
        // Set up real-time match subscription
        const matchSubscription = db.subscribeToMatch(matchId, (payload) => {
          const updatedMatch = payload.new;
          setBattle(updatedMatch);
          
          // Update opponent score if this is a PvP match
          if (!match.is_bot_match && updatedMatch.player2_id) {
            const isPlayer1 = user.id === updatedMatch.player1_id;
            const newOpponentScore = isPlayer1 ? updatedMatch.player2_score : updatedMatch.player1_score;
            // Update opponent score in store if needed
          }
        });
        
      } catch (error) {
        console.error('Error loading battle:', error);
        toast.error('Failed to load battle');
        navigate('/home');
      } finally {
        setLoading(false);
      }
    };

    loadBattle();
  }, [matchId, user, navigate, setBattle, setQuestions, startQuestionTimer, isAnswered]);

  // Handle connection status
  useEffect(() => {
    const handleOnline = () => {
      setIsConnected(true);
      if (matchId && user) {
        reconnectToBattle(matchId, user.id);
      }
    };

    const handleOffline = () => {
      setIsConnected(false);
      handleDisconnection();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [matchId, user, reconnectToBattle, handleDisconnection]);

  useEffect(() => {
    if (!battle || !currentQuestionData) return;

    // Auto-advance to next question immediately after answering (no 3-second delay)
    if (isAnswered) {
      const timer = setTimeout(() => {
        if (currentQuestion < 9) {
          nextQuestion();
        } else {
          setShowResults(true);
          if (score > opponentScore) {
            setShowConfetti(true);
            setTimeout(() => setShowConfetti(false), 5000);
          }
        }
      }, 1500); // Reduced from 3000ms to 1500ms

      return () => clearTimeout(timer);
    }
  }, [isAnswered, currentQuestion, nextQuestion, score, opponentScore]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopQuestionTimer();
      cleanupSubscriptions();
    };
  }, [cleanupSubscriptions, stopQuestionTimer]);

  const handleAnswer = (answerIndex: number) => {
    if (!user || !battle || isAnswered) return;
    answerQuestion(answerIndex, user.id);
  };

  const handleTimeUp = () => {
    if (!isAnswered) {
      handleAnswer(-1); // No answer selected
    }
  };

  const handleEndBattle = () => {
    endBattle();
    resetGame();
    navigate('/home');
  };

  const handleQuitBattle = () => {
    if (window.confirm('Are you sure you want to quit this battle? You will lose your progress.')) {
      endBattle();
      resetGame();
      navigate('/home');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 flex items-center justify-center">
        <Card className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading battle...</p>
        </Card>
      </div>
    );
  }

  if (!battle || !currentQuestionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 flex items-center justify-center">
        <Card className="p-8 text-center">
          <p className="text-gray-600 mb-4">Battle not found</p>
          <Button onClick={() => navigate('/home')}>Return Home</Button>
        </Card>
      </div>
    );
  }

  const isWinner = score > opponentScore;
  const isTie = score === opponentScore;
  const correctAnswerIndex = parseInt(currentQuestionData.correct_answer);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-emerald-600 p-4">
      {showConfetti && <Confetti />}
      
      <div className="max-w-4xl mx-auto">
        {/* Battle Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white rounded-xl shadow-lg p-4 mb-6"
        >
          <div className="flex items-center justify-between">
            {/* Quit button */}
            <button
              onClick={handleQuitBattle}
              className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="text-sm">Quit</span>
            </button>

            <div className="flex items-center space-x-8">
              {/* Player 1 Score */}
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{score}</div>
                <div className="text-sm text-gray-600">{user?.username}</div>
                <div className="text-xs text-gray-500">You</div>
              </div>
              
              {/* Question Counter with Timer */}
              <div className="text-center">
                <Crown className="w-8 h-8 text-yellow-500 mx-auto mb-1" />
                <div className="text-lg font-semibold text-gray-700">
                  {currentQuestion + 1}/10
                </div>
                <div className="text-xs text-gray-500">
                  {currentQuestionData.category}
                </div>
                
                {/* Enhanced Timer Display */}
                <div className="mt-2">
                  <div className={`text-2xl font-bold ${syncedTimeLeft <= 5 ? 'text-red-500' : 'text-blue-600'}`}>
                    {syncedTimeLeft}s
                  </div>
                  <div className="w-16 h-2 bg-gray-200 rounded-full mx-auto mt-1">
                    <div 
                      className={`h-full rounded-full transition-all duration-100 ${
                        syncedTimeLeft <= 5 ? 'bg-red-500' : 'bg-blue-500'
                      }`}
                      style={{ width: `${(syncedTimeLeft / 15) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
              
              {/* Player 2 Score */}
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">{opponentScore}</div>
                <div className="text-sm text-gray-600">
                  {battle.is_bot_match || battle.is_solo_match ? 'Bot' : opponent?.username || 'Opponent'}
                </div>
                <div className="text-xs text-gray-500">
                  {battle.is_bot_match || battle.is_solo_match ? 'AI' : 'Player'}
                </div>
              </div>
            </div>

            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className="w-5 h-5 text-green-500" />
              ) : (
                <WifiOff className="w-5 h-5 text-red-500" />
              )}
              <span className={`text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {isConnected ? 'Connected' : 'Reconnecting...'}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Opponent Info Banner (for PvP matches) */}
        {!battle.is_bot_match && !battle.is_solo_match && opponent && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-4 mb-6 text-center"
          >
            <div className="flex items-center justify-center space-x-3">
              <Users className="w-5 h-5" />
              <span className="font-semibold">
                Battling against {opponent.username}
              </span>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
            </div>
          </motion.div>
        )}

        {/* Question Card */}
        <AnimatePresence mode="wait">
          {!showResults ? (
            <motion.div
              key={currentQuestion}
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="p-6 mb-6">
                <div className="text-center mb-6">
                  <div className="text-sm text-purple-600 font-medium mb-2">
                    Question {currentQuestion + 1} of 10
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {currentQuestionData.question}
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentQuestionData.options.map((option, index) => {
                    let buttonStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-900 border-2 border-transparent';
                    
                    if (isAnswered) {
                      if (index === correctAnswerIndex) {
                        buttonStyle = 'bg-green-500 text-white border-green-600'; // Correct answer
                      } else if (index === selectedAnswer && index !== correctAnswerIndex) {
                        buttonStyle = 'bg-red-500 text-white border-red-600'; // Wrong selected answer
                      } else {
                        buttonStyle = 'bg-gray-200 text-gray-600 border-gray-300'; // Other options
                      }
                    } else if (selectedAnswer === index) {
                      buttonStyle = 'bg-purple-100 border-purple-500 text-purple-900'; // Selected but not answered yet
                    }

                    return (
                      <motion.button
                        key={index}
                        whileHover={!isAnswered ? { scale: 1.02 } : undefined}
                        whileTap={!isAnswered ? { scale: 0.98 } : undefined}
                        onClick={() => handleAnswer(index)}
                        disabled={isAnswered || !isConnected}
                        className={`p-4 rounded-lg transition-all duration-200 text-left font-medium ${buttonStyle} ${
                          !isConnected ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-sm font-bold">
                            {String.fromCharCode(65 + index)}
                          </div>
                          <span className="flex-1">{option}</span>
                          {isAnswered && index === correctAnswerIndex && (
                            <CheckCircle className="w-5 h-5 ml-auto" />
                          )}
                          {isAnswered && index === selectedAnswer && index !== correctAnswerIndex && (
                            <XCircle className="w-5 h-5 ml-auto" />
                          )}
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {isAnswered && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 text-center"
                  >
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Timer className="w-5 h-5 text-gray-500" />
                      <span className="text-gray-600">
                        {currentQuestion < 9 ? 'Next question in 1.5s' : 'Showing results in 1.5s'}
                      </span>
                    </div>
                    
                    {selectedAnswer === correctAnswerIndex ? (
                      <div className="text-green-600 font-semibold">
                        ✅ Correct! +10 points
                      </div>
                    ) : selectedAnswer === -1 ? (
                      <div className="text-orange-600 font-semibold">
                        ⏰ Time's up! No points
                      </div>
                    ) : (
                      <div className="text-red-600 font-semibold">
                        ❌ Incorrect! No points
                      </div>
                    )}
                  </motion.div>
                )}
              </Card>
            </motion.div>
          ) : (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center"
            >
              <Card className="p-8">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="mb-6"
                >
                  {isWinner ? (
                    <div className="text-6xl">🏆</div>
                  ) : isTie ? (
                    <div className="text-6xl">🤝</div>
                  ) : (
                    <div className="text-6xl">😔</div>
                  )}
                </motion.div>

                <h2 className={`text-3xl font-bold mb-4 ${
                  isWinner ? 'text-green-600' : isTie ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {isWinner ? 'Victory!' : isTie ? 'Draw!' : 'Defeat!'}
                </h2>

                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-purple-600">{score}</div>
                      <div className="text-sm text-gray-600">Your Score</div>
                      <div className="text-xs text-gray-500">{score * 10} points</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-gray-400">VS</div>
                      <div className="text-sm text-gray-600">Battle</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-red-500">{opponentScore}</div>
                      <div className="text-sm text-gray-600">
                        {battle.is_bot_match || battle.is_solo_match ? 'Bot' : opponent?.username || 'Opponent'}
                      </div>
                      <div className="text-xs text-gray-500">{opponentScore * 10} points</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Performance stats */}
                <div className="bg-blue-50 rounded-lg p-4 mb-6">
                  <div className="text-sm text-gray-600 mb-2">Performance</div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Accuracy:</span>
                      <span className="font-semibold text-blue-600">
                        {Math.round((score / 10) * 100)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Correct Answers:</span>
                      <span className="font-semibold text-green-600">
                        {score}/10
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Points Earned:</span>
                      <span className="font-semibold text-purple-600">
                        {score * 10 + (isWinner ? 100 : 0)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Coins Earned:</span>
                      <span className="font-semibold text-yellow-600">
                        {score + (isWinner ? 100 : 0)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={handleEndBattle}
                    className="w-full"
                    size="lg"
                  >
                    Return to Home
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};