import { create } from 'zustand';
import { supabase, db } from '../lib/supabase';
import { Battle, Question, QuizCategory, GameState } from '../types';
import toast from 'react-hot-toast';

interface GameStore extends GameState {
  battle: Battle | null;
  isSearching: boolean;
  searchTimeLeft: number;
  questions: Question[];
  currentQuestionData: Question | null;
  matchmakingSubscription: any;
  matchSubscription: any;
  userStatsSubscription: any;
  searchTimer: NodeJS.Timeout | null;
  matchmakingInterval: NodeJS.Timeout | null;
  questionTimer: NodeJS.Timeout | null;
  isLoading: boolean;
  questionStartTime: number;
  syncedTimeLeft: number;
  
  // Actions
  startMatchmaking: (category: QuizCategory, userId: string) => Promise<void>;
  stopMatchmaking: (userId: string) => Promise<void>;
  joinBattleRoom: (battleId: string, userId: string) => Promise<void>;
  createFriendBattle: (category: QuizCategory, userId: string) => Promise<string>;
  joinFriendBattle: (roomCode: string, userId: string) => Promise<void>;
  startSoloBattle: (category: QuizCategory, userId: string) => Promise<void>;
  answerQuestion: (answerIndex: number, userId: string) => Promise<void>;
  nextQuestion: () => void;
  endBattle: () => void;
  resetGame: () => void;
  cleanupSubscriptions: () => void;
  forceCloseMatchmaking: () => void;
  setBattle: (battle: Battle) => void;
  setQuestions: (questions: Question[]) => void;
  updateSearchTime: (time: number) => void;
  startQuestionTimer: () => void;
  stopQuestionTimer: () => void;
  handleDisconnection: () => void;
  reconnectToBattle: (matchId: string, userId: string) => Promise<void>;
}

const initialGameState: GameState = {
  currentQuestion: 0,
  timeLeft: 15,
  score: 0,
  opponentScore: 0,
  isAnswered: false,
  selectedAnswer: undefined,
  questions: [],
};

export const useGameStore = create<GameStore>((set, get) => ({
  ...initialGameState,
  battle: null,
  isSearching: false,
  searchTimeLeft: 30,
  currentQuestionData: null,
  matchmakingSubscription: null,
  matchSubscription: null,
  userStatsSubscription: null,
  searchTimer: null,
  matchmakingInterval: null,
  questionTimer: null,
  isLoading: false,
  questionStartTime: 0,
  syncedTimeLeft: 15,

  setBattle: (battle: Battle) => {
    console.log('🎮 Setting battle in store:', battle.id);
    set({ battle });
  },

  setOpponentScore: (score: number) => {
    set({ opponentScore: score });
  },

  setSyncedTimeLeft: (timeLeft: number) => {
    set({ syncedTimeLeft: timeLeft });
  },

  setQuestions: (questions: Question[]) => {
    console.log('📝 Setting questions in store:', questions.length);
    set({ 
      questions,
      currentQuestionData: questions[0] || null,
      currentQuestion: 0,
      score: 0,
      opponentScore: 0,
      isAnswered: false,
      selectedAnswer: undefined,
      timeLeft: 15,
      syncedTimeLeft: 15
    });
  },

  updateSearchTime: (time: number) => {
    console.log('🕐 Updating search time in store to:', time);
    set({ searchTimeLeft: time });
  },

  startQuestionTimer: () => {
    const { questionTimer } = get();
    if (questionTimer) {
      clearInterval(questionTimer);
    }

    const startTime = Date.now();
    set({ 
      questionStartTime: startTime,
      timeLeft: 15,
      syncedTimeLeft: 15
    });

    const timer = setInterval(() => {
      const elapsed = Math.floor((Date.now() - startTime) / 1000);
      const remaining = Math.max(0, 15 - elapsed);
      
      set({ 
        timeLeft: remaining,
        syncedTimeLeft: remaining
      });

      if (remaining <= 0) {
        clearInterval(timer);
        const { isAnswered } = get();
        if (!isAnswered) {
          // Auto-submit with no answer
          get().answerQuestion(-1, ''); // Will be handled by the component
        }
      }
    }, 100); // Update every 100ms for smooth countdown

    set({ questionTimer: timer });
  },

  stopQuestionTimer: () => {
    const { questionTimer } = get();
    if (questionTimer) {
      clearInterval(questionTimer);
      set({ questionTimer: null });
    }
  },

  startMatchmaking: async (category: QuizCategory, userId: string) => {
    try {
      console.log('🎯 Starting enhanced matchmaking for category:', category, 'user:', userId);
      
      // Prevent multiple simultaneous matchmaking attempts
      const currentState = get();
      if (currentState.isSearching || currentState.isLoading) {
        console.log('⚠️ Matchmaking already in progress, ignoring request');
        return;
      }
      
      // Clean up any existing state first
      get().cleanupSubscriptions();
      
      // Set initial state
      set({ 
        isSearching: true, 
        searchTimeLeft: 30,
        isLoading: false,
        battle: null,
        questions: [],
        currentQuestionData: null
      });
      
      // Start countdown timer
      console.log('⏰ Starting countdown timer from 30 seconds...');
      let currentTime = 30;
      
      // Clear any existing timer first
      const existingTimer = get().searchTimer;
      if (existingTimer) {
        clearInterval(existingTimer);
      }
      
      const timer = setInterval(() => {
        const { isSearching } = get();
        
        if (!isSearching) {
          console.log('🛑 Search cancelled, clearing timer');
          clearInterval(timer);
          return;
        }
        
        currentTime--;
        console.log(`⏰ Timer tick: ${currentTime} seconds remaining`);
        
        set({ searchTimeLeft: currentTime });
        
        if (currentTime <= 0) {
          console.log('⏰ Matchmaking timeout reached, starting solo battle');
          clearInterval(timer);
          get().stopMatchmaking(userId);
          setTimeout(() => {
            get().startSoloBattle(category, userId);
          }, 500);
          return;
        }
      }, 1000);
      
      set({ searchTimer: timer });
      
      // Validate user session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('No active session. Please sign in again.');
      }
      
      // Join matchmaking queue
      console.log('📝 Joining matchmaking queue...');
      try {
        await db.joinMatchmaking(userId, category);
        console.log('✅ Successfully joined matchmaking queue');
      } catch (error) {
        console.error('❌ Failed to join matchmaking queue:', error);
        throw new Error('Failed to join matchmaking queue: ' + error.message);
      }
      
      // Check for immediate match
      console.log('🔍 Checking for immediate match...');
      let existingMatch = null;
      try {
        existingMatch = await db.findAvailableMatch(userId, category);
        console.log('🔍 Available match search result:', existingMatch);
      } catch (error) {
        console.error('❌ Error finding available match:', error);
      }
      
      if (existingMatch) {
        console.log('⚡ Found immediate match with user:', existingMatch.user_id);
        
        try {
          // Create a match and join it
          const { match, questions } = await db.createMatch(userId, category);
          const updatedMatch = await db.joinMatch(match.id, existingMatch.user_id);
          
          // Remove both players from queue
          await Promise.all([
            db.removeFromMatchmaking(userId),
            db.removeFromMatchmaking(existingMatch.user_id)
          ]);
          
          console.log('✅ Both players removed from queue after successful match');
          
          // Clear the timer
          clearInterval(timer);
          
          set({ 
            isSearching: false, 
            battle: updatedMatch,
            questions,
            currentQuestionData: questions[0] || null,
            searchTimer: null,
            matchmakingInterval: null,
            isLoading: false
          });
          
          toast.success('🎮 Match found! Battle starting...');
          return;
        } catch (matchError) {
          console.error('❌ Error creating immediate match:', matchError);
          // Continue with subscription if immediate match fails
        }
      }
      
      console.log('⏳ No immediate match found, setting up real-time subscription...');
      
      // Set up real-time subscription for new players joining
      const subscription = db.subscribeToMatchmaking(category, async (payload) => {
        console.log('📡 Matchmaking subscription triggered:', payload);
        
        const { eventType, new: newPlayer, old: oldPlayer } = payload;
        const currentState = get();
        
        // Handle player leaving the queue
        if (eventType === 'DELETE' && oldPlayer?.user_id === userId) {
          console.log('👋 Player left the queue');
          return;
        }
        
        // Handle new player joining
        if (eventType === 'INSERT' && newPlayer && newPlayer.user_id !== userId && newPlayer.category === category && newPlayer.is_active && currentState.isSearching) {
          console.log('👤 New player joined queue:', newPlayer.user_id);

          // Prevent race condition by immediately setting isSearching to false
          set({ isSearching: false });

          try {
            // Create a match and join it
            const { match, questions } = await db.createMatch(userId, category);
            const updatedMatch = await db.joinMatch(match.id, newPlayer.user_id);

            // Remove both players from queue
            await Promise.all([
              db.removeFromMatchmaking(userId),
              db.removeFromMatchmaking(newPlayer.user_id)
            ]);

            console.log('✅ Both players removed from queue after subscription match');

            // Clean up subscription and timer
            get().cleanupSubscriptions();
            clearInterval(timer);
            const { matchmakingInterval } = get();
            if (matchmakingInterval) clearInterval(matchmakingInterval);

            set({
              battle: updatedMatch,
              questions,
              currentQuestionData: questions[0] || null,
              searchTimer: null,
              matchmakingInterval: null,
              isLoading: false
            });

            toast.success('🎮 Match found! Battle starting...');
          } catch (error) {
            console.error('❌ Error in matchmaking subscription:', error);
            // Reset searching state on error
            set({ isSearching: true });
            toast.error('Failed to create match. Continuing search...');
          }
        }
      });

      // Set up polling fallback every 3 seconds
      const pollingInterval = setInterval(async () => {
        const currentState = get();
        if (!currentState.isSearching) {
          clearInterval(pollingInterval);
          return;
        }

        console.log('🔄 Polling for available matches...');
        try {
          const availableMatch = await db.findAvailableMatch(userId, category);
          if (availableMatch) {
            console.log('🎯 Found match via polling:', availableMatch.user_id);

            // Prevent race condition
            set({ isSearching: false });

            try {
              // Create a match and join it
              const { match, questions } = await db.createMatch(userId, category);
              const updatedMatch = await db.joinMatch(match.id, availableMatch.user_id);

              // Remove both players from queue
              await Promise.all([
                db.removeFromMatchmaking(userId),
                db.removeFromMatchmaking(availableMatch.user_id)
              ]);

              console.log('✅ Both players removed from queue after polling match');

              // Clean up
              get().cleanupSubscriptions();
              clearInterval(timer);
              clearInterval(pollingInterval);

              set({
                battle: updatedMatch,
                questions,
                currentQuestionData: questions[0] || null,
                searchTimer: null,
                matchmakingInterval: null,
                isLoading: false
              });

              toast.success('🎮 Match found! Battle starting...');
            } catch (error) {
              console.error('❌ Error creating match from polling:', error);
              set({ isSearching: true });
            }
          }
        } catch (error) {
          console.error('❌ Error during polling:', error);
        }
      }, 3000);

      set({
        matchmakingSubscription: subscription,
        matchmakingInterval: pollingInterval
      });

    } catch (error: any) {
      console.error('❌ Matchmaking error:', error);
      
      // Clean up any existing state
      get().cleanupSubscriptions();
      
      // Reset state
      set({ 
        isSearching: false, 
        isLoading: false,
        searchTimer: null,
        matchmakingInterval: null
      });
      
      throw error;
    }
  },

  stopMatchmaking: async (userId: string) => {
    console.log('🛑 Stopping matchmaking for user:', userId);

    // Immediately update frontend state to show cancellation
    set({
      isSearching: false,
      isLoading: true // Show loading while cleaning up
    });

    // Clear any existing timers first
    const { searchTimer, matchmakingInterval } = get();
    if (searchTimer) {
      clearInterval(searchTimer);
      console.log('🧹 Cleared search timer');
    }
    if (matchmakingInterval) {
      clearInterval(matchmakingInterval);
      console.log('🧹 Cleared matchmaking interval');
    }

    // Clean up subscriptions first
    get().cleanupSubscriptions();

    // Try to remove from matchmaking queue
    try {
      await db.removeFromMatchmaking(userId);
      console.log('✅ Successfully removed from matchmaking queue');
    } catch (error) {
      console.error('❌ Error removing from matchmaking queue:', error);
      // Continue with cleanup even if this fails
    }

    // Reset all matchmaking related state
    set({
      isSearching: false,
      searchTimeLeft: 30,
      isLoading: false,
      searchTimer: null,
      matchmakingInterval: null,
      battle: null,
      questions: [],
      currentQuestionData: null,
      matchmakingSubscription: null,
      matchSubscription: null,
      userStatsSubscription: null
    });

    console.log('✅ Matchmaking stopped successfully');
  },

  forceCloseMatchmaking: () => {
    console.log('🔄 Force closing matchmaking...');
    
    // Clear any existing timers
    const { searchTimer, matchmakingInterval } = get();
    if (searchTimer) {
      clearInterval(searchTimer);
    }
    if (matchmakingInterval) {
      clearInterval(matchmakingInterval);
    }
    
    // Clean up subscriptions
    get().cleanupSubscriptions();
    
    // Reset all matchmaking related state
    set({ 
      isSearching: false, 
      searchTimeLeft: 30,
      isLoading: false,
      searchTimer: null,
      matchmakingInterval: null,
      battle: null,
      questions: [],
      currentQuestionData: null,
      matchmakingSubscription: null,
      matchSubscription: null,
      userStatsSubscription: null
    });
    
    console.log('✅ Matchmaking force closed');
  },

  joinBattleRoom: async (battleId: string, userId: string) => {
    try {
      const battle = await db.joinMatch(battleId, userId);
      const { questions } = await db.getMatchWithQuestions(battleId);

      set({ 
        battle, 
        questions,
        currentQuestionData: questions[0] || null 
      });

      // Set up real-time match subscription
      const matchSubscription = db.subscribeToMatch(battleId, (payload) => {
        const updatedMatch = payload.new;
        set({ battle: updatedMatch });
      });

      set({ matchSubscription });
    } catch (error: any) {
      console.error('Error joining battle room:', error);
      toast.error('Failed to join battle: ' + error.message);
    }
  },

  createFriendBattle: async (category: QuizCategory, userId: string) => {
    try {
      const roomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      const { match, questions } = await db.createMatch(userId, category, false, roomCode);
      
      set({ 
        battle: match, 
        questions,
        currentQuestionData: questions[0] || null 
      });
      
      return roomCode;
    } catch (error: any) {
      console.error('Error creating friend battle:', error);
      toast.error('Failed to create room: ' + error.message);
      throw error;
    }
  },

  joinFriendBattle: async (roomCode: string, userId: string) => {
    try {
      const { data: battle, error } = await supabase
        .from('matches')
        .select('*')
        .eq('room_code', roomCode)
        .eq('status', 'waiting')
        .single();

      if (error) throw new Error('Room not found or already started');

      await get().joinBattleRoom(battle.id, userId);
      toast.success('Joined battle room!');
    } catch (error: any) {
      console.error('Error joining friend battle:', error);
      toast.error('Failed to join room: ' + error.message);
    }
  },

  startSoloBattle: async (category: QuizCategory, userId: string) => {
    try {
      console.log('🤖 Starting solo battle for category:', category);
      
      // Clean up any matchmaking state
      get().cleanupSubscriptions();
      
      const { match, questions } = await db.createMatch(userId, category, true);
      
      set({ 
        battle: match,
        questions,
        currentQuestionData: questions[0] || null,
        isSearching: false,
        searchTimer: null,
        matchmakingInterval: null,
        questionTimer: null,
        isLoading: false
      });
      
      toast.success('🤖 Starting solo battle vs Bot!');
    } catch (error: any) {
      console.error('Error starting solo battle:', error);
      toast.error('Failed to start solo battle: ' + error.message);
    }
  },

  answerQuestion: async (answerIndex: number, userId: string) => {
    const { battle, currentQuestionData, score, currentQuestion, questionStartTime } = get();
    if (!battle || !currentQuestionData || get().isAnswered) return;

    // Stop the question timer
    get().stopQuestionTimer();

    const timeTaken = Math.floor((Date.now() - questionStartTime) / 1000);
    const isCorrect = answerIndex.toString() === currentQuestionData.correct_answer;
    const pointsEarned = isCorrect ? 10 : 0; // 10 points per correct answer
    const newScore = score + pointsEarned;

    set({ 
      isAnswered: true, 
      selectedAnswer: answerIndex,
      score: newScore 
    });

    // Record the answer in the database
    try {
      await db.recordMatchAnswer(
        battle.id,
        userId,
        currentQuestionData.id,
        answerIndex.toString(),
        isCorrect,
        timeTaken
      );
    } catch (error) {
      console.error('Error recording match answer:', error);
    }

    // Update battle score in database for PvP matches
    if (battle.player2_id && !battle.is_bot_match) {
      try {
        await db.updateMatchScore(battle.id, userId, newScore);
      } catch (error) {
        console.error('Error updating match score:', error);
      }
    }

    // Simulate bot answer for solo battles
    if (battle.is_bot_match || battle.is_solo_match) {
      setTimeout(() => {
        const botCorrect = Math.random() > 0.3; // Bot has 70% accuracy
        const botPoints = botCorrect ? 10 : 0;
        const botScore = get().opponentScore + botPoints;
        set({ opponentScore: botScore });
      }, Math.random() * 2000 + 1000); // Random delay 1-3 seconds
    }
  },

  nextQuestion: () => {
    const { currentQuestion, questions } = get();
    if (currentQuestion < questions.length - 1) {
      set({
        currentQuestion: currentQuestion + 1,
        currentQuestionData: questions[currentQuestion + 1],
        timeLeft: 15,
        syncedTimeLeft: 15,
        isAnswered: false,
        selectedAnswer: undefined,
      });
      
      // Start timer for the new question
      get().startQuestionTimer();
    } else {
      get().endBattle();
    }
  },

  endBattle: async () => {
    const { score, opponentScore, battle } = get();
    if (!battle) return;

    const won = score > opponentScore;
    const tied = score === opponentScore;
    
    // Stop all timers
    get().stopQuestionTimer();
    
    try {
      // Complete the match in database
      const winnerId = won ? battle.player1_id : (tied ? undefined : battle.player2_id);
      await db.completeMatch(battle.id, winnerId);

      // Calculate rewards
      const baseCoins = score; // 1 coin per point
      const winBonus = won ? 100 : 0; // 100 bonus points for winner
      const totalCoins = baseCoins + winBonus;
      const experienceGained = score * 2;

      // Update user stats and record history
      const userId = battle.player1_id; // Assuming current user is player1
      await db.updateUserStats(userId, won, totalCoins, score + winBonus);
      
      // Record coin transaction
      await db.recordCoinTransaction(
        userId,
        totalCoins,
        won ? 'match_win' : 'match_participation',
        won ? 'Victory bonus and participation reward' : 'Participation reward',
        battle.id
      );

      // Record game history
      const opponentName = battle.is_bot_match ? 'Bot' : 'Opponent';
      const result = won ? 'win' : (tied ? 'draw' : 'loss');
      const matchDuration = battle.start_time ? 
        `${Math.floor((Date.now() - new Date(battle.start_time).getTime()) / 1000)} seconds` : 
        '0 seconds';

      await db.recordGameHistory(
        battle.id,
        userId,
        battle.player2_id || battle.player1_id,
        opponentName,
        score,
        opponentScore,
        result,
        totalCoins,
        experienceGained,
        matchDuration
      );

      if (tied) {
        toast.success('🤝 It\'s a tie!', { duration: 3000 });
      } else {
        toast.success(won ? `🎉 You Won! +${totalCoins} coins!` : '😔 You Lost!', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error ending battle:', error);
      toast.error('Error saving battle results');
    }

    // Clean up subscriptions
    get().cleanupSubscriptions();
  },

  handleDisconnection: () => {
    console.log('🔌 Handling disconnection...');
    toast.error('Connection lost. Attempting to reconnect...');
    
    // Stop timers but keep battle state
    get().stopQuestionTimer();
    
    // Try to reconnect after a short delay
    setTimeout(() => {
      const { battle } = get();
      if (battle) {
        // Attempt to reconnect to the battle
        // This would typically involve re-establishing WebSocket connections
        console.log('🔄 Attempting to reconnect to battle...');
      }
    }, 2000);
  },

  reconnectToBattle: async (matchId: string, userId: string) => {
    try {
      console.log('🔄 Reconnecting to battle:', matchId);
      
      const { match, questions } = await db.getMatchWithQuestions(matchId);
      
      set({
        battle: match,
        questions,
        currentQuestionData: questions[get().currentQuestion] || null
      });

      // Re-establish real-time subscriptions
      const matchSubscription = db.subscribeToMatch(matchId, (payload) => {
        const updatedMatch = payload.new;
        set({ battle: updatedMatch });
      });

      set({ matchSubscription });
      
      toast.success('Reconnected to battle!');
    } catch (error) {
      console.error('Error reconnecting to battle:', error);
      toast.error('Failed to reconnect to battle');
    }
  },

  resetGame: () => {
    console.log('🔄 Resetting game state');
    
    // Clean up subscriptions and timers
    get().cleanupSubscriptions();
    
    set({
      ...initialGameState,
      battle: null,
      isSearching: false,
      searchTimeLeft: 30,
      currentQuestionData: null,
      matchmakingSubscription: null,
      matchSubscription: null,
      userStatsSubscription: null,
      searchTimer: null,
      matchmakingInterval: null,
      questionTimer: null,
      isLoading: false,
      questionStartTime: 0,
      syncedTimeLeft: 15,
    });
    
    console.log('✅ Game state reset completed');
  },

  cleanupSubscriptions: () => {
    console.log('🧹 Cleaning up subscriptions...');
    try {
      const { matchmakingSubscription, matchmakingInterval } = get();
      
      // Clean up matchmaking subscription
      if (matchmakingSubscription && typeof matchmakingSubscription === 'function') {
        console.log('🧹 Cleaning up matchmaking subscription...');
        matchmakingSubscription();
      }
      
      // Clean up matchmaking interval
    if (matchmakingInterval) {
        console.log('🧹 Cleaning up matchmaking interval...');
      clearInterval(matchmakingInterval);
    }

      // Reset subscription state
    set({ 
      matchmakingSubscription: null, 
        matchmakingInterval: null
      });
      
      console.log('✅ Subscriptions cleaned up');
    } catch (error) {
      console.error('❌ Error cleaning up subscriptions:', error);
      // Force reset subscription state even if cleanup fails
      set({ 
        matchmakingSubscription: null,
        matchmakingInterval: null
    });
    }
  },
}));