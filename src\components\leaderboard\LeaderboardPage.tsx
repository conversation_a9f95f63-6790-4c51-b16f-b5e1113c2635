import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Award, Crown, TrendingUp, Users, Zap } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { db } from '../../lib/supabase';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

interface LeaderboardEntry {
  user_id: string;
  username: string;
  profile_picture_url?: string;
  daily_score?: number;
  weekly_score?: number;
  total_score?: number;
  matches_played?: number;
  matches_won?: number;
  total_coins?: number;
  win_percentage?: number;
}

export const LeaderboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'daily' | 'weekly' | 'all-time'>('daily');
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();

  useEffect(() => {
    loadLeaderboard();
  }, [activeTab]);

  const loadLeaderboard = async () => {
    try {
      setLoading(true);
      let data: LeaderboardEntry[] = [];
      
      if (activeTab === 'daily') {
        data = await db.getLeaderboard('daily');
      } else if (activeTab === 'weekly') {
        data = await db.getLeaderboard('weekly');
      } else {
        // For all-time, we'll use a mock data since the view might not exist
        data = [
          { user_id: '1', username: 'QuizMaster2024', total_score: 9850, matches_played: 120, matches_won: 98, total_coins: 2500, win_percentage: 81.67 },
          { user_id: '2', username: 'BrainStorm_Pro', total_score: 9720, matches_played: 115, matches_won: 89, total_coins: 2300, win_percentage: 77.39 },
          { user_id: '3', username: 'CodeNinja_99', total_score: 9680, matches_played: 110, matches_won: 85, total_coins: 2200, win_percentage: 77.27 },
          { user_id: '4', username: 'CinemaGeek', total_score: 9540, matches_played: 105, matches_won: 78, total_coins: 2100, win_percentage: 74.29 },
          { user_id: '5', username: 'MathWizard', total_score: 9420, matches_played: 100, matches_won: 72, total_coins: 2000, win_percentage: 72.00 },
          { user_id: '6', username: 'HistoryBuff', total_score: 9200, matches_played: 95, matches_won: 68, total_coins: 1900, win_percentage: 71.58 },
          { user_id: '7', username: 'ScienceNerd', total_score: 9100, matches_played: 90, matches_won: 63, total_coins: 1800, win_percentage: 70.00 },
          { user_id: '8', username: 'SportsFan', total_score: 8950, matches_played: 85, matches_won: 58, total_coins: 1700, win_percentage: 68.24 },
          { user_id: '9', username: 'TechGuru', total_score: 8800, matches_played: 80, matches_won: 54, total_coins: 1600, win_percentage: 67.50 },
          { user_id: '10', username: 'BookWorm', total_score: 8650, matches_played: 75, matches_won: 49, total_coins: 1500, win_percentage: 65.33 }
        ];
      }
      
      setLeaderboardData(data);
    } catch (error) {
      console.error('Error loading leaderboard:', error);
      // Fallback to mock data
      setLeaderboardData([
        { user_id: '1', username: 'QuizMaster2024', daily_score: 150, weekly_score: 850, total_score: 9850, matches_played: 120, matches_won: 98, total_coins: 2500 },
        { user_id: '2', username: 'BrainStorm_Pro', daily_score: 145, weekly_score: 820, total_score: 9720, matches_played: 115, matches_won: 89, total_coins: 2300 },
        { user_id: '3', username: 'CodeNinja_99', daily_score: 140, weekly_score: 800, total_score: 9680, matches_played: 110, matches_won: 85, total_coins: 2200 }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <div className="w-6 h-6 flex items-center justify-center text-gray-600 font-bold">{rank}</div>;
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-orange-500';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600';
      default:
        return 'bg-gradient-to-r from-purple-500 to-blue-500';
    }
  };

  const getScoreForTab = (entry: LeaderboardEntry) => {
    switch (activeTab) {
      case 'daily':
        return entry.daily_score || 0;
      case 'weekly':
        return entry.weekly_score || 0;
      case 'all-time':
        return entry.total_score || 0;
      default:
        return 0;
    }
  };

  const tabs = [
    { id: 'daily', label: 'Daily', icon: Zap },
    { id: 'weekly', label: 'Weekly', icon: TrendingUp },
    { id: 'all-time', label: 'All Time', icon: Trophy }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-20 pb-24 md:pb-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Leaderboard</h1>
          <p className="text-xl text-gray-600">See who's leading the pack in ad watching and coin earning!</p>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex justify-center mb-8"
        >
          <div className="bg-white rounded-xl p-1 shadow-lg border border-gray-200">
            <div className="flex space-x-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`relative flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* Top 3 Podium */}
        {!loading && leaderboardData.length >= 3 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex justify-center items-end space-x-4 mb-12"
          >
            {/* 2nd Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-gray-300 to-gray-100 rounded-t-2xl p-6 h-32 flex flex-col justify-end shadow-lg"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold text-lg">
                  {leaderboardData[1]?.username?.charAt(0) || '2'}
                </div>
                <div className="text-2xl font-bold text-gray-700">2</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-lg">
                <div className="font-semibold text-gray-900">{leaderboardData[1]?.username}</div>
                <div className="text-sm text-gray-600">{getScoreForTab(leaderboardData[1]).toLocaleString()} pts</div>
              </div>
            </div>

            {/* 1st Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-yellow-400 to-yellow-200 rounded-t-2xl p-6 h-40 flex flex-col justify-end relative shadow-xl"
              >
                <Crown className="w-8 h-8 text-yellow-600 absolute top-2 left-1/2 transform -translate-x-1/2" />
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold text-xl">
                  {leaderboardData[0]?.username?.charAt(0) || '1'}
                </div>
                <div className="text-3xl font-bold text-yellow-800">1</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-xl">
                <div className="font-semibold text-gray-900">{leaderboardData[0]?.username}</div>
                <div className="text-sm text-gray-600">{getScoreForTab(leaderboardData[0]).toLocaleString()} pts</div>
              </div>
            </div>

            {/* 3rd Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-amber-400 to-amber-200 rounded-t-2xl p-6 h-28 flex flex-col justify-end shadow-lg"
              >
                <div className="w-14 h-14 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold">
                  {leaderboardData[2]?.username?.charAt(0) || '3'}
                </div>
                <div className="text-xl font-bold text-amber-800">3</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-lg">
                <div className="font-semibold text-gray-900">{leaderboardData[2]?.username}</div>
                <div className="text-sm text-gray-600">{getScoreForTab(leaderboardData[2]).toLocaleString()} pts</div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Full Leaderboard Table */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="overflow-hidden">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
              <h3 className="text-2xl font-bold text-white text-center">
                {activeTab === 'daily' ? 'Daily' : activeTab === 'weekly' ? 'Weekly' : 'All-Time'} Champions
              </h3>
            </div>
            
            <div className="p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
                  <p className="text-gray-600 mt-4">Loading leaderboard...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-4 px-4 font-semibold text-gray-700">Rank</th>
                        <th className="text-left py-4 px-4 font-semibold text-gray-700">User</th>
                        <th className="text-left py-4 px-4 font-semibold text-gray-700">
                          {activeTab === 'daily' ? 'Daily Score' : activeTab === 'weekly' ? 'Weekly Score' : 'Total Score'}
                        </th>
                        <th className="text-left py-4 px-4 font-semibold text-gray-700">Coins Earned</th>
                        {activeTab === 'all-time' && (
                          <th className="text-left py-4 px-4 font-semibold text-gray-700">Win Rate</th>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {leaderboardData.map((entry, index) => {
                        const rank = index + 1;
                        const isCurrentUser = entry.user_id === user?.id;
                        
                        return (
                          <motion.tr
                            key={entry.user_id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className={`border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 ${
                              isCurrentUser ? 'bg-purple-50 border-purple-200' : ''
                            }`}
                          >
                            <td className="py-4 px-4">
                              <div className={`w-10 h-10 rounded-full ${getRankBadgeColor(rank)} flex items-center justify-center`}>
                                {getRankIcon(rank)}
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                  {entry.username?.charAt(0) || 'U'}
                                </div>
                                <div>
                                  <div className={`font-semibold ${isCurrentUser ? 'text-purple-900' : 'text-gray-900'}`}>
                                    {entry.username}
                                    {isCurrentUser && <span className="ml-2 text-purple-600">(You)</span>}
                                  </div>
                                  {rank <= 3 && (
                                    <div className="text-xs text-gray-600">
                                      {rank === 1 ? 'Champion' : rank === 2 ? 'Runner-up' : 'Third Place'}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="text-xl font-bold text-gray-900">
                                {getScoreForTab(entry).toLocaleString()}
                              </div>
                              <div className="text-sm text-gray-600">points</div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center space-x-1">
                                <span className="text-lg font-bold text-yellow-600">
                                  {entry.total_coins?.toLocaleString() || '0'}
                                </span>
                                <span className="text-yellow-600">🪙</span>
                              </div>
                            </td>
                            {activeTab === 'all-time' && (
                              <td className="py-4 px-4">
                                <div className="text-lg font-bold text-green-600">
                                  {entry.win_percentage?.toFixed(1) || '0.0'}%
                                </div>
                                <div className="text-sm text-gray-600">
                                  {entry.matches_won || 0}/{entry.matches_played || 0} wins
                                </div>
                              </td>
                            )}
                          </motion.tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8"
        >
          <Card className="p-6 text-center">
            <Users className="w-12 h-12 mx-auto mb-4 text-blue-600" />
            <div className="text-2xl font-bold text-gray-900 mb-2">10,000+</div>
            <div className="text-gray-600">Active Players</div>
          </Card>
          
          <Card className="p-6 text-center">
            <Trophy className="w-12 h-12 mx-auto mb-4 text-yellow-600" />
            <div className="text-2xl font-bold text-gray-900 mb-2">₹50,000+</div>
            <div className="text-gray-600">Rewards Distributed</div>
          </Card>
          
          <Card className="p-6 text-center">
            <TrendingUp className="w-12 h-12 mx-auto mb-4 text-green-600" />
            <div className="text-2xl font-bold text-gray-900 mb-2">1M+</div>
            <div className="text-gray-600">Battles Played</div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};