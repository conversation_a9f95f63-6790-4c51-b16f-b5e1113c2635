import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Award, Crown } from 'lucide-react';

export const LeaderboardPreview: React.FC = () => {
  const topPlayers = [
    {
      rank: 1,
      username: 'QuizMaster2024',
      score: 9850,
      avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      badge: 'Legendary',
      streak: 15,
    },
    {
      rank: 2,
      username: 'BrainStorm_Pro',
      score: 9720,
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      badge: 'Expert',
      streak: 12,
    },
    {
      rank: 3,
      username: 'CodeNinja_99',
      score: 9680,
      avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      badge: 'Master',
      streak: 8,
    },
    {
      rank: 4,
      username: 'CinemaGeek',
      score: 9540,
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      badge: 'Expert',
      streak: 6,
    },
    {
      rank: 5,
      username: 'MathWizard',
      score: 9420,
      avatar: 'https://images.pexels.com/photos/1043473/pexels-photo-1043473.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      badge: 'Master',
      streak: 10,
    },
  ];

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <Trophy className="w-5 h-5 text-gray-500" />;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-orange-500';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600';
      default:
        return 'bg-gradient-to-r from-purple-500 to-blue-500';
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Global <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Leaderboard</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Compete with the best quiz masters from around the world and climb your way to the top
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* Podium for Top 3 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="flex justify-center items-end space-x-4 mb-12"
          >
            {/* 2nd Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-gray-300 to-gray-100 rounded-t-2xl p-6 h-32 flex flex-col justify-end"
              >
                <img
                  src={topPlayers[1].avatar}
                  alt={topPlayers[1].username}
                  className="w-16 h-16 rounded-full mx-auto mb-2 border-4 border-white shadow-lg"
                />
                <div className="text-2xl font-bold text-gray-700">2</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-lg">
                <div className="font-semibold text-gray-900">{topPlayers[1].username}</div>
                <div className="text-sm text-gray-600">{topPlayers[1].score.toLocaleString()} pts</div>
              </div>
            </div>

            {/* 1st Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-yellow-400 to-yellow-200 rounded-t-2xl p-6 h-40 flex flex-col justify-end relative"
              >
                <Crown className="w-8 h-8 text-yellow-600 absolute top-2 left-1/2 transform -translate-x-1/2" />
                <img
                  src={topPlayers[0].avatar}
                  alt={topPlayers[0].username}
                  className="w-20 h-20 rounded-full mx-auto mb-2 border-4 border-white shadow-lg"
                />
                <div className="text-3xl font-bold text-yellow-800">1</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-lg">
                <div className="font-semibold text-gray-900">{topPlayers[0].username}</div>
                <div className="text-sm text-gray-600">{topPlayers[0].score.toLocaleString()} pts</div>
              </div>
            </div>

            {/* 3rd Place */}
            <div className="text-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-t from-amber-400 to-amber-200 rounded-t-2xl p-6 h-28 flex flex-col justify-end"
              >
                <img
                  src={topPlayers[2].avatar}
                  alt={topPlayers[2].username}
                  className="w-14 h-14 rounded-full mx-auto mb-2 border-4 border-white shadow-lg"
                />
                <div className="text-xl font-bold text-amber-800">3</div>
              </motion.div>
              <div className="bg-white p-4 rounded-b-2xl shadow-lg">
                <div className="font-semibold text-gray-900">{topPlayers[2].username}</div>
                <div className="text-sm text-gray-600">{topPlayers[2].score.toLocaleString()} pts</div>
              </div>
            </div>
          </motion.div>

          {/* Full Leaderboard Table */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white rounded-2xl shadow-lg overflow-hidden"
          >
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
              <h3 className="text-2xl font-bold text-white text-center">Weekly Champions</h3>
            </div>
            
            <div className="p-6">
              {topPlayers.map((player, index) => (
                <motion.div
                  key={player.username}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02 }}
                  className={`flex items-center justify-between p-4 rounded-xl mb-3 transition-all duration-300 ${
                    player.rank <= 3 ? 'bg-gradient-to-r from-purple-50 to-blue-50 border-2 border-purple-200' : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-full ${getRankColor(player.rank)} flex items-center justify-center text-white font-bold`}>
                      {player.rank <= 3 ? getRankIcon(player.rank) : player.rank}
                    </div>
                    
                    <img
                      src={player.avatar}
                      alt={player.username}
                      className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
                    />
                    
                    <div>
                      <div className="font-semibold text-gray-900">{player.username}</div>
                      <div className="text-sm text-gray-600 flex items-center space-x-2">
                        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                          {player.badge}
                        </span>
                        <span className="text-orange-600">🔥 {player.streak} streak</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-xl font-bold text-gray-900">
                      {player.score.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">points</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
              <Trophy className="w-16 h-16 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Think You Can Beat Them?
              </h3>
              <p className="text-lg text-purple-100 mb-6 max-w-2xl mx-auto">
                Join the competition and prove your quiz mastery. Climb the leaderboard and earn amazing rewards!
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-purple-900 font-bold px-8 py-3 rounded-lg hover:bg-yellow-300 transition-colors duration-300"
              >
                Start Climbing Now
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};