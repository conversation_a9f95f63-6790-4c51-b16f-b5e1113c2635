import React, { useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Clock, Zap, X, Loader2, Users, Target } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { toast } from 'react-hot-toast';

export const MatchmakingScreen: React.FC = () => {
  const { 
    isSearching, 
    searchTimeLeft, 
    stopMatchmaking, 
    forceCloseMatchmaking,
    isLoading,
    resetGame,
    searchTimer
  } = useGameStore();
  const { user } = useAuthStore();
  const [isCancelling, setIsCancelling] = React.useState(false);

  const handleCancel = async () => {
    if (isCancelling || !user) return;
    
    try {
      setIsCancelling(true);
      console.log('🛑 Cancel button clicked, stopping matchmaking for user:', user.id);
      
      // Stop matchmaking first
      await stopMatchmaking(user.id);
      
      // Force cleanup of any stuck state
      forceCloseMatchmaking();
      
      // Clear any existing timers
      if (searchTimer) {
        clearInterval(searchTimer);
      }
      
      // Reset game state
      resetGame();
      
      // Use replace instead of href for more reliable navigation
      window.location.href = '/';
    } catch (error) {
      console.error('Error cancelling matchmaking:', error);
      toast.error('Failed to cancel matchmaking. Please try again.');
      // Force navigation even if there's an error
      window.location.href = '/';
    } finally {
      setIsCancelling(false);
    }
  };

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isSearching) {
        console.log('⌨️ Escape key pressed, cancelling search');
        handleCancel();
      }
    };

    if (isSearching) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isSearching, handleCancel]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 MatchmakingScreen unmounting, cleaning up...');
      const { cleanupSubscriptions } = useGameStore.getState();
      cleanupSubscriptions();
    };
  }, []);

  // Handle background click to close
  const handleBackgroundClick = useCallback((event: React.MouseEvent) => {
    if (event.target === event.currentTarget && !isLoading) {
      console.log('🖱️ Background clicked, cancelling search');
      handleCancel();
    }
  }, [handleCancel, isLoading]);

  // Don't render if not searching
  if (!isSearching) {
    // Force navigation if somehow we're still mounted but not searching
    window.location.replace('/');
    return null;
  }

  // Calculate progress percentage for animations
  const progressPercentage = (searchTimeLeft / 30) * 100;
  const isUrgent = searchTimeLeft <= 10;

  return (
    <AnimatePresence>
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        onClick={handleBackgroundClick}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative"
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the modal
        >
          <Card className="p-8 text-center max-w-md relative overflow-hidden">
            {/* Animated background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-emerald-50 opacity-50"></div>
            
            {/* Close button */}
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed z-10"
              aria-label="Cancel search"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Main content */}
            <div className="relative z-10">
              {/* Animated search icon with pulsing effect */}
              <motion.div
                animate={{ 
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  rotate: { duration: 3, repeat: Infinity, ease: "linear" },
                  scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                }}
                className="w-20 h-20 mx-auto mb-6 relative"
              >
                <div className={`absolute inset-0 rounded-full ${isUrgent ? 'bg-red-100' : 'bg-purple-100'} animate-ping`}></div>
                <div className={`relative w-20 h-20 rounded-full ${isUrgent ? 'bg-red-500' : 'bg-purple-600'} flex items-center justify-center`}>
                  <Search className="w-10 h-10 text-white" />
                </div>
              </motion.div>

              <motion.h2 
                animate={{ opacity: [0.8, 1, 0.8] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-2xl font-bold text-gray-900 mb-2"
              >
                Finding Opponent...
              </motion.h2>
              
              <p className="text-gray-600 mb-6">
                Searching for players ready to battle
              </p>

              {/* Large timer display with color changes */}
              <div className="flex items-center justify-center space-x-3 mb-6">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  <Clock className={`w-6 h-6 ${isUrgent ? 'text-red-500' : 'text-purple-500'}`} />
                </motion.div>
                <motion.span 
                  key={searchTimeLeft} // Key change triggers animation
                  initial={{ scale: 1.2, color: isUrgent ? '#ef4444' : '#8b5cf6' }}
                  animate={{ scale: 1, color: isUrgent ? '#ef4444' : '#8b5cf6' }}
                  transition={{ duration: 0.3 }}
                  className="text-4xl font-bold"
                >
                  {searchTimeLeft}s
                </motion.span>
              </div>

              {/* Animated progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-4 mb-6 overflow-hidden shadow-inner">
                <motion.div
                  initial={{ width: '100%' }}
                  animate={{ 
                    width: `${progressPercentage}%`,
                    backgroundColor: isUrgent ? '#ef4444' : '#8b5cf6'
                  }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  className="h-full rounded-full relative"
                >
                  {/* Animated shine effect */}
                  <motion.div
                    animate={{ x: [-100, 200] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 w-20"
                  />
                </motion.div>
              </div>

              {/* Status messages with icons */}
              <div className="space-y-3 mb-6">
                <motion.div
                  animate={{ opacity: [0.6, 1, 0.6] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="flex items-center justify-center space-x-2 text-sm text-gray-700"
                >
                  <motion.div 
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="w-3 h-3 bg-green-500 rounded-full"
                  ></motion.div>
                  <span className="font-medium">Searching for opponents...</span>
                </motion.div>
                
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <Users className="w-4 h-4" />
                  <span>Looking for players in your category</span>
                </div>
                
                <motion.div 
                  animate={{ opacity: isUrgent ? [0.5, 1, 0.5] : 0.7 }}
                  transition={{ duration: 1, repeat: isUrgent ? Infinity : 0 }}
                  className={`flex items-center justify-center space-x-2 text-sm ${isUrgent ? 'text-orange-600 font-semibold' : 'text-gray-500'}`}
                >
                  <Zap className="w-4 h-4" />
                  <span>Auto-switching to Solo Battle in {searchTimeLeft}s</span>
                </motion.div>
              </div>

              {/* Cancel button with loading state */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isLoading}
                  className="w-full mb-6"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Cancelling...
                    </>
                  ) : (
                    <>
                      <X className="w-4 h-4 mr-2" />
                      Cancel Search
                    </>
                  )}
                </Button>
              </motion.div>

              {/* Tips and info */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
                className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100"
              >
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <Target className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-800">Matchmaking Tips</span>
                </div>
                <p className="text-xs text-blue-700 mb-2">
                  💡 <strong>Peak hours (6-10 PM):</strong> Faster matchmaking!
                </p>
                <p className="text-xs text-blue-600">
                  ⌨️ Press <kbd className="px-1.5 py-0.5 bg-white rounded text-xs border border-blue-200 font-mono">Esc</kbd> to cancel
                </p>
              </motion.div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};