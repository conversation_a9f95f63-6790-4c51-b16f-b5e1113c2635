/*
  # Fix Users Table RLS Policies and Authentication

  1. Database Schema Updates
    - Ensure all required columns exist with proper defaults
    - Add proper constraints and indexes
  
  2. RLS Policy Updates
    - Enable authenticated users to insert their own profile (auth.uid() = user_id)
    - Allow users to read their own data
    - Allow reading public profile data for leaderboards/matchmaking
    - Prevent multiple rows for same user with unique constraint
  
  3. Trigger Function
    - Auto-create user profile after Supabase auth signup
    - Handle edge cases and prevent duplicates
    - Set proper default values
*/

-- First, ensure the users table has all required columns with proper defaults
DO $$
BEGIN
  -- Ensure created_at exists with proper default
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE users ADD COLUMN created_at timestamptz DEFAULT now();
  END IF;
  
  -- Ensure updated_at exists with proper default
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE users ADD COLUMN updated_at timestamptz DEFAULT now();
  END IF;

  -- Ensure total_coins has proper default
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'total_coins' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN total_coins SET DEFAULT 100;
  END IF;

  -- Ensure other score columns have proper defaults
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'total_score' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN total_score SET DEFAULT 0;
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'daily_score' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN daily_score SET DEFAULT 0;
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'weekly_score' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN weekly_score SET DEFAULT 0;
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'matches_played' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN matches_played SET DEFAULT 0;
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'matches_won' AND column_default IS NULL
  ) THEN
    ALTER TABLE users ALTER COLUMN matches_won SET DEFAULT 0;
  END IF;
END $$;

-- Drop ALL existing policies to start completely fresh
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read public profiles" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Enable insert for authenticated users during signup" ON public.users;
DROP POLICY IF EXISTS "Enable read access for own user data" ON public.users;
DROP POLICY IF EXISTS "Enable read access for public user profiles" ON public.users;
DROP POLICY IF EXISTS "Enable update for own user data" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile during signup" ON public.users;
DROP POLICY IF EXISTS "Users can read own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
DROP POLICY IF EXISTS "Allow reading public user profiles" ON public.users;

-- Ensure RLS is enabled on the users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies with proper auth.uid() usage
-- Policy 1: Allow authenticated users to insert their own profile during signup
CREATE POLICY "Users can insert own profile"
  ON public.users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Policy 2: Allow users to read their own profile data
CREATE POLICY "Users can read own profile"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Policy 3: Allow users to update their own profile data
CREATE POLICY "Users can update own profile"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy 4: Allow authenticated users to read public profile data (for leaderboards, matchmaking, etc.)
CREATE POLICY "Users can read public profiles"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (true);

-- Create or replace the trigger function for automatic user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insert user profile with proper error handling
  INSERT INTO public.users (
    id, 
    email, 
    username, 
    profile_picture_url,
    total_coins,
    total_score,
    daily_score,
    weekly_score,
    matches_played,
    matches_won,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(
      NEW.raw_user_meta_data->>'username', 
      split_part(NEW.email, '@', 1),
      'User'
    ),
    NEW.raw_user_meta_data->>'avatar_url',
    100, -- Default starting coins
    0,   -- Default total score
    0,   -- Default daily score
    0,   -- Default weekly score
    0,   -- Default matches played
    0,   -- Default matches won
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING; -- Prevent duplicate inserts
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the auth process
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists and create new one
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create or replace updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger to users table
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON public.users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);

-- Ensure foreign key constraint exists for auth integration
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'users_id_fkey' AND table_name = 'users'
  ) THEN
    ALTER TABLE public.users 
    ADD CONSTRAINT users_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
  END IF;
END $$;