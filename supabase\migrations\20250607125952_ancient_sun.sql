/*
  # Fix RLS policies for users table

  1. Security Updates
    - Drop existing policies that use incorrect uid() function
    - Create new policies using correct auth.uid() function
    - Ensure all CRUD operations are properly secured
    - Add policy for public profile viewing (for leaderboards)

  2. Policy Changes
    - Enable authenticated users to read their own data
    - Enable authenticated users to insert their own profile during signup
    - Enable authenticated users to update their own data
    - Enable authenticated users to view public profile data for leaderboards
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Enable insert for authenticated users during signup" ON public.users;
DROP POLICY IF EXISTS "Enable read access for own user data" ON public.users;
DROP POLICY IF EXISTS "Enable read access for public user profiles" ON public.users;
DROP POLICY IF EXISTS "Enable update for own user data" ON public.users;

-- Create new policies with correct auth.uid() function
CREATE POLICY "Users can insert own profile"
  ON public.users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can read own profile"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Allow authenticated users to read public profile data (for leaderboards, matchmaking, etc.)
CREATE POLICY "Users can read public profiles"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (true);

-- Ensure RLS is enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;