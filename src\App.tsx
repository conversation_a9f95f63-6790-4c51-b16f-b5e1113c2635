import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { useAuthStore } from './store/authStore';
import { useGameStore } from './store/gameStore';
import { LandingPage } from './components/landing/LandingPage';
import { AuthForm } from './components/auth/AuthForm';
import { Navbar } from './components/layout/Navbar';
import { HomePage } from './components/home/<USER>';
import { ProfilePage } from './components/profile/ProfilePage';
import { LeaderboardPage } from './components/leaderboard/LeaderboardPage';
import { BattleScreen } from './components/game/BattleScreen';
import { BattlePage } from './components/game/BattlePage';
import { MatchmakingPage } from './components/game/MatchmakingPage';

function App() {
  const { user, loading, initialize } = useAuthStore();
  const { battle, isSearching } = useGameStore();
  const [showAuth, setShowAuth] = useState(false);

  useEffect(() => {
    initialize();
  }, [initialize]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  // Show landing page if user is not authenticated and auth form is not shown
  if (!user && !showAuth) {
    return (
      <>
        <LandingPage onGetStarted={() => setShowAuth(true)} />
        <Toaster position="top-center" />
      </>
    );
  }

  // Show auth form if requested or user is not authenticated
  if (!user) {
    return (
      <>
        <AuthForm onSuccess={() => {
          setShowAuth(false);
          // User will be automatically redirected to home page via auth state change
        }} />
        <Toaster position="top-center" />
      </>
    );
  }

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        
        <Routes>
          <Route path="/" element={<Navigate to="/home" replace />} />
          <Route path="/home" element={<HomePage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/leaderboard" element={<LeaderboardPage />} />
          <Route path="/matchmaking" element={<MatchmakingPage />} />
          <Route path="/battle/:matchId" element={<BattlePage />} />
          <Route path="/quiz" element={
            battle ? <BattleScreen /> : <Navigate to="/home" replace />
          } />
          <Route path="*" element={<Navigate to="/home" replace />} />
        </Routes>
        
        <Toaster position="top-center" />
      </div>
    </Router>
  );
}

export default App;