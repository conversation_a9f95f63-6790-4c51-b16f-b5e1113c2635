import React from 'react';
import { motion } from 'framer-motion';
import { HeroSection } from './HeroSection';
import { FeaturesSection } from './FeaturesSection';
import { HowItWorksSection } from './HowItWorksSection';
import { TestimonialsSection } from './TestimonialsSection';
import { LeaderboardPreview } from './LeaderboardPreview';
import { RewardsSection } from './RewardsSection';
import { FAQSection } from './FAQSection';
import { Footer } from './Footer';

interface LandingPageProps {
  onGetStarted: () => void;
}

export const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen"
    >
      <HeroSection onGetStarted={onGetStarted} />
      <FeaturesSection />
      <HowItWorksSection />
      <TestimonialsSection />
      <LeaderboardPreview />
      <RewardsSection />
      <FAQSection />
      <Footer />
    </motion.div>
  );
};