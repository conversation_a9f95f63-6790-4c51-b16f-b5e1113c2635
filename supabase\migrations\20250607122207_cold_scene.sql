/*
  # QuizBattle Pro Database Schema

  1. New Tables
    - `profiles` - User profiles extending auth.users
    - `questions` - Quiz questions with categories and difficulty
    - `battles` - Game battles between players
    - `leaderboard_entries` - Daily/weekly leaderboard data
    - `matchmaking_queue` - Real-time matchmaking system

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Secure data access based on user ownership

  3. Sample Data
    - Insert initial questions for all categories
    - Set up proper indexes for performance
*/

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid REFERENCES auth.users PRIMARY KEY,
  username text UNIQUE NOT NULL,
  email text NOT NULL,
  avatar_url text,
  total_wins integer DEFAULT 0,
  total_games integer DEFAULT 0,
  best_category text,
  current_rank integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Create questions table
CREATE TABLE IF NOT EXISTS questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question text NOT NULL,
  options jsonb NOT NULL,
  correct_answer integer NOT NULL,
  category text NOT NULL,
  difficulty text DEFAULT 'medium',
  created_at timestamptz DEFAULT now()
);

-- Create battles table
CREATE TABLE IF NOT EXISTS battles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  player1_id uuid REFERENCES profiles(id) NOT NULL,
  player2_id uuid REFERENCES profiles(id),
  category text NOT NULL,
  questions jsonb NOT NULL,
  player1_score integer DEFAULT 0,
  player2_score integer DEFAULT 0,
  current_question integer DEFAULT 0,
  status text DEFAULT 'waiting',
  winner_id uuid REFERENCES profiles(id),
  room_code text,
  created_at timestamptz DEFAULT now()
);

-- Create leaderboard entries table
CREATE TABLE IF NOT EXISTS leaderboard_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  score integer NOT NULL,
  category text NOT NULL,
  period text NOT NULL,
  date date NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create matchmaking queue table
CREATE TABLE IF NOT EXISTS matchmaking_queue (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  category text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE battles ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE matchmaking_queue ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can read all profiles" ON profiles FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE TO authenticated USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);

-- RLS Policies for questions
CREATE POLICY "Anyone can read questions" ON questions FOR SELECT TO authenticated USING (true);

-- RLS Policies for battles
CREATE POLICY "Users can read own battles" ON battles FOR SELECT TO authenticated 
  USING (auth.uid() = player1_id OR auth.uid() = player2_id);
CREATE POLICY "Users can create battles" ON battles FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = player1_id);
CREATE POLICY "Users can update own battles" ON battles FOR UPDATE TO authenticated 
  USING (auth.uid() = player1_id OR auth.uid() = player2_id);

-- RLS Policies for leaderboard
CREATE POLICY "Anyone can read leaderboard" ON leaderboard_entries FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can insert own entries" ON leaderboard_entries FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for matchmaking
CREATE POLICY "Users can read all queue entries" ON matchmaking_queue FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can insert own queue entry" ON matchmaking_queue FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own queue entry" ON matchmaking_queue FOR DELETE TO authenticated 
  USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_questions_category ON questions(category);
CREATE INDEX IF NOT EXISTS idx_battles_status ON battles(status);
CREATE INDEX IF NOT EXISTS idx_battles_room_code ON battles(room_code);
CREATE INDEX IF NOT EXISTS idx_matchmaking_category ON matchmaking_queue(category);
CREATE INDEX IF NOT EXISTS idx_matchmaking_created_at ON matchmaking_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_leaderboard_period_category ON leaderboard_entries(period, category);