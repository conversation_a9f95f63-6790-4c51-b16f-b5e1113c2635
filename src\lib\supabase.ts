import { createClient } from '@supabase/supabase-js';
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
export const db = {
  // User functions
  async getUserProfile(userId: string) {
    // Ensure we have a valid session before making the request
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No active session');
    }

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('getUserProfile error:', error);
      throw error;
    }
    return data;
  },

  async ensureUserProfileExists(userId: string, userData?: any) {
    try {
      return await this.getUserProfile(userId);
    } catch (error: any) {
      // If profile doesn't exist and we have user data, create it
      if (error.code === 'PGRST116' && userData) {
        return await this.createUserProfile(userId, userData);
      }
      throw error;
    }
  },

  async updateUserProfile(userId: string, updates: any) {
    // Ensure we have a valid session before making the request
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No active session');
    }

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('updateUserProfile error:', error);
      throw error;
    }
    return data;
  },

  async createUserProfile(userId: string, userData: any) {
    // Ensure we have a valid session before making the request
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No active session');
    }

    const { data, error } = await supabase
      .from('users')
      .insert({
        id: userId,
        username: userData.username,
        email: userData.email,
        profile_picture_url: userData.profile_picture_url || null,
        total_coins: userData.total_coins || 100,
        total_score: userData.total_score || 0,
        daily_score: userData.daily_score || 0,
        weekly_score: userData.weekly_score || 0,
        matches_played: userData.matches_played || 0,
        matches_won: userData.matches_won || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error('createUserProfile error:', error);
      throw error;
    }
    return data;
  },

  // Question functions
  async getRandomQuestions(category: string, count: number = 10) {
    const { data, error } = await supabase
      .from('quiz_questions')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(count * 2); // Get more questions to randomize
    
    if (error) {
      console.error('getRandomQuestions error:', error);
      throw error;
    }
    
    // Shuffle and return the requested count
    const shuffled = (data || []).sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
  },

  // Enhanced Battle functions with real-time capabilities
  async createMatch(userId: string, category: string, isBot: boolean = false, roomCode?: string) {
    const questions = await this.getRandomQuestions(category);
    
    const { data, error } = await supabase
      .from('matches')
      .insert({
        player1_id: userId,
        match_type: roomCode ? 'pvp' : (isBot ? 'bot' : 'pvp'),
        status: 'waiting',
        questions_assigned: questions.map(q => q.id),
        is_bot_match: isBot,
        is_solo_match: isBot,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error('createMatch error:', error);
      throw error;
    }
    return { match: data, questions };
  },

  async joinMatch(matchId: string, userId: string) {
    const { data, error } = await supabase
      .from('matches')
      .update({
        player2_id: userId,
        status: 'in_progress',
        start_time: new Date().toISOString()
      })
      .eq('id', matchId)
      .eq('status', 'waiting') // Only join if still waiting
      .select()
      .single();
    
    if (error) {
      console.error('joinMatch error:', error);
      throw error;
    }
    return data;
  },

  async updateMatchScore(matchId: string, playerId: string, score: number) {
    const isPlayer1 = await this.isPlayer1(matchId, playerId);
    const field = isPlayer1 ? 'player1_score' : 'player2_score';
    
    const { data, error } = await supabase
      .from('matches')
      .update({ [field]: score })
      .eq('id', matchId)
      .select()
      .single();
    
    if (error) {
      console.error('updateMatchScore error:', error);
      throw error;
    }
    return data;
  },

  async completeMatch(matchId: string, winnerId?: string) {
    const { data, error } = await supabase
      .from('matches')
      .update({
        status: 'completed',
        end_time: new Date().toISOString(),
        winner_id: winnerId
      })
      .eq('id', matchId)
      .select()
      .single();
    
    if (error) {
      console.error('completeMatch error:', error);
      throw error;
    }
    return data;
  },

  async recordMatchAnswer(matchId: string, playerId: string, questionId: string, selectedAnswer: string, isCorrect: boolean, timeTaken: number) {
    const { data, error } = await supabase
      .from('match_answers')
      .insert({
        match_id: matchId,
        player_id: playerId,
        question_id: questionId,
        selected_answer: selectedAnswer,
        is_correct: isCorrect,
        time_taken: timeTaken,
        answered_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error('recordMatchAnswer error:', error);
      throw error;
    }
    return data;
  },

  async updateUserStats(userId: string, won: boolean, coinsEarned: number, scoreEarned: number) {
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching user stats:', fetchError);
      throw fetchError;
    }

    const updates = {
      matches_played: currentUser.matches_played + 1,
      matches_won: won ? currentUser.matches_won + 1 : currentUser.matches_won,
      total_coins: currentUser.total_coins + coinsEarned,
      total_score: currentUser.total_score + scoreEarned,
      daily_score: currentUser.daily_score + scoreEarned,
      weekly_score: currentUser.weekly_score + scoreEarned,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('updateUserStats error:', error);
      throw error;
    }
    return data;
  },

  async recordGameHistory(matchId: string, playerId: string, opponentId: string, opponentName: string, playerScore: number, opponentScore: number, result: string, coinsEarned: number, experienceGained: number, matchDuration: string) {
    const { data, error } = await supabase
      .from('game_history')
      .insert({
        match_id: matchId,
        player_id: playerId,
        opponent_id: opponentId,
        opponent_name: opponentName,
        player_score: playerScore,
        opponent_score: opponentScore,
        result: result,
        coins_earned: coinsEarned,
        experience_gained: experienceGained,
        match_duration: matchDuration,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error('recordGameHistory error:', error);
      throw error;
    }
    return data;
  },

  async recordCoinTransaction(userId: string, amount: number, transactionType: string, reason: string, matchId?: string) {
    const { data, error } = await supabase
      .from('coin_transactions')
      .insert({
        user_id: userId,
        amount: amount,
        transaction_type: transactionType,
        reason: reason,
        match_id: matchId,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error('recordCoinTransaction error:', error);
      throw error;
    }
    return data;
  },

  async isPlayer1(matchId: string, userId: string) {
    const { data, error } = await supabase
      .from('matches')
      .select('player1_id')
      .eq('id', matchId)
      .single();
    
    if (error) {
      console.error('isPlayer1 error:', error);
      throw error;
    }
    return data?.player1_id === userId;
  },

  // Enhanced matchmaking with self-match prevention
  async joinMatchmaking(userId: string, category: string) {
    console.log('🔄 Joining matchmaking queue for user:', userId, 'category:', category);
    // Remove any existing entries for this user
    try {
      await this.removeFromMatchmaking(userId);
      console.log('🧹 Cleaned up any existing queue entries');
    } catch (error) {
      console.log('ℹ️ No existing queue entry to remove (this is normal)');
    }
    // Insert or update entry with is_active: true
    const { data, error } = await supabase
      .from('matchmaking_queue')
      .upsert({
        user_id: userId,
        category,
        created_at: new Date().toISOString(),
        is_active: true,
        last_active_at: new Date().toISOString()
      }, { onConflict: ['user_id', 'category'] })
      .select('id, user_id, category, created_at, is_active, last_active_at')
      .single();
    if (error) {
      console.error('❌ joinMatchmaking error:', error);
      throw error;
    }
    console.log('✅ Successfully joined matchmaking queue:', data);
    return data;
  },

  async removeFromMatchmaking(userId: string) {
    console.log('🗑️ Removing user from matchmaking queue:', userId);
    // Set is_active: false instead of deleting
    const { error } = await supabase
      .from('matchmaking_queue')
      .update({ is_active: false })
      .eq('user_id', userId);
    if (error) {
      console.error('❌ Error removing from matchmaking queue:', error);
      throw error;
    }
    console.log('✅ Successfully removed from matchmaking queue');
  },

  async findAvailableMatch(userId: string, category: string) {
    console.log('🔍 Finding available match for user:', userId, 'category:', category);
    try {
      const { data, error } = await supabase
        .from('matchmaking_queue')
        .select('id, user_id, category, created_at, is_active, last_active_at')
        .eq('category', category)
        .eq('is_active', true)
        .neq('user_id', userId)
        .order('created_at', { ascending: true })
        .limit(1);
      if (error) {
        console.error('❌ findAvailableMatch error:', error);
        throw error;
      }
      if (!data || data.length === 0) {
        console.log('ℹ️ No available match found');
        return null;
      }
      console.log('✅ Found available match:', data[0]);
      return data[0];
    } catch (error) {
      console.error('❌ Error in findAvailableMatch:', error);
      return null;
    }
  },

  areEmailsSimilar(email1: string, email2: string): boolean {
    // Check if emails are too similar (same base with numbers)
    const base1 = email1.replace(/\d+$/, '');
    const base2 = email2.replace(/\d+$/, '');
    return base1 === base2 && base1.length > 3;
  },

  async getMatchWithQuestions(matchId: string) {
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .select('*')
      .eq('id', matchId)
      .single();

    if (matchError) {
      console.error('getMatchWithQuestions error:', matchError);
      throw matchError;
    }

    // Get questions for this match
    const { data: questions, error: questionsError } = await supabase
      .from('quiz_questions')
      .select('*')
      .in('id', match.questions_assigned);

    if (questionsError) {
      console.error('getMatchQuestions error:', questionsError);
      throw questionsError;
    }

    return { match, questions: questions || [] };
  },

  // Enhanced real-time subscriptions
  subscribeToMatchmaking(category: string, callback: (payload: any) => void) {
    console.log('📡 Setting up matchmaking subscription for category:', category);
    
    const channelName = `matchmaking_${category}_${Date.now()}`;
    console.log('📡 Creating channel:', channelName);
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'matchmaking_queue',
          filter: `category=eq.${category}`
        },
        (payload) => {
          console.log('📡 Matchmaking event received:', payload);
          callback(payload);
        }
      )
      .subscribe((status) => {
        console.log('📡 Matchmaking subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to matchmaking channel');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Error subscribing to matchmaking channel');
          // Attempt to resubscribe after a delay
          setTimeout(() => {
            console.log('🔄 Attempting to resubscribe...');
            channel.subscribe();
          }, 5000);
        }
      });
    
    // Set up health check
    const healthCheck = setInterval(() => {
      if (channel.state === 'SUBSCRIBED') {
        console.log('✅ Matchmaking subscription health check: OK');
      } else {
        console.warn('⚠️ Matchmaking subscription health check: Not subscribed');
        // Attempt to resubscribe
        channel.subscribe();
      }
    }, 30000);
    
    // Return cleanup function
    return () => {
      console.log('🧹 Cleaning up matchmaking subscription...');
      clearInterval(healthCheck);
      channel.unsubscribe();
    };
  },

  subscribeToMatch(matchId: string, callback: (payload: any) => void) {
    console.log('📡 Setting up match subscription for match:', matchId);
    
    const channelName = `match_${matchId}_${Date.now()}`;
    console.log('📡 Creating match channel:', channelName);
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'matches',
          filter: `id=eq.${matchId}`
        },
        (payload) => {
          console.log('📡 Match subscription received UPDATE:', payload);
          callback(payload);
        }
      )
      .subscribe((status, err) => {
        console.log('📡 Match subscription status:', status);
        if (err) {
          console.error('📡 Match subscription error:', err);
        } else if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to match channel');
        }
      });
      
    return channel;
  },

  subscribeToUserStats(userId: string, callback: (payload: any) => void) {
    console.log('📡 Setting up user stats subscription for user:', userId);
    
    const channelName = `user_stats_${userId}_${Date.now()}`;
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${userId}`
        },
        (payload) => {
          console.log('📡 User stats subscription received UPDATE:', payload);
          callback(payload);
        }
      )
      .subscribe((status, err) => {
        console.log('📡 User stats subscription status:', status);
        if (err) {
          console.error('📡 User stats subscription error:', err);
        } else if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to user stats channel');
        }
      });
      
    return channel;
  },

  // Leaderboard functions
  async getLeaderboard(period: 'daily' | 'weekly', category?: string) {
    const viewName = period === 'daily' ? 'daily_leaderboard' : 'weekly_leaderboard';
    
    const { data, error } = await supabase
      .from(viewName)
      .select('*')
      .order(`${period}_score`, { ascending: false })
      .limit(10);

    if (error) {
      console.error('getLeaderboard error:', error);
      throw error;
    }
    return data || [];
  }
};